{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@google/generative-ai": "^0.24.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-table": "^8.21.2", "@types/multer": "^1.4.13", "@vercel/analytics": "^1.5.0", "autoprefixer": "10.4.20", "axios": "^1.8.4", "boxen": "^7.1.1", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.3", "clsx": "^2.1.1", "commander": "^11.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.3.1", "figlet": "^1.7.0", "framer-motion": "^12.6.3", "gradient-string": "^2.0.2", "iron-session": "^8.0.4", "lucide-react": "^0.487.0", "multer": "^2.0.1", "next": "latest", "next-themes": "^0.4.3", "openai": "^4.86.1", "ora": "^7.0.1", "papaparse": "^5.5.3", "prettier": "^3.3.3", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-social-icons": "^6.22.0", "react-tweet": "^3.2.2", "recharts": "^2.15.2", "sonner": "^2.0.3", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@types/node": "22.10.2", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "postcss": "8.4.49", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}, "type": "module"}
# Metrics Dashboard Wiring Guide

> Last updated: July 2025

This guide explains **how to replace all mock data in the Metrics Dashboard with live data pulled from Supabase**, using the utilities already committed to the codebase.

---

## 1 Server-side fetchers (`actions.ts`)

File: `src/app/(dashboard-pages)/metrics/actions.ts`

- One fetcher per table is exported (typed).
- `fetchMetricsDashboard()` runs them in **parallel** and returns an aggregated object:

```ts
{
  siteActivation: SiteActivationRow[];
  onboardingFunnel: OnboardingFunnelRow[];
  safariMetrics: SafariExtensionMetricsRow[];
  dailyMetrics: DailyMetricsRow[];
  cohortRetention: CohortRetentionRow[];
}
```

If you need additional filters, extend the individually-scoped fetcher instead of adding SQL inside components.

---

## 2 Pass data to the client component (page.tsx)

```tsx
// src/app/(dashboard-pages)/metrics/page.tsx
const data = await fetchMetricsDashboard();
return <MetricsPageClient data={data} />;
```

The page remains a **Server Component** so the database call never hits the client.

---

## 3 Prop-driven widgets

Each widget gets a lightweight prop interface that mirrors the slice of data it actually draws.

Example – Site Activation (already refactored):

```tsx
export interface SiteActivationWidgetProps {
  data?: {
    hostname: string | null;
    phiaShown: number;
    phiaClickedWithBookmark: number;
    activationRate: number;
  }[];
  platform?: string;
}
```

If `data` is **undefined** the widget falls back to its original mock dataset (handy for Storybook/design).

### Mapping Supabase rows to widget props

Inside `src/app/(dashboard-pages)/metrics/metrics-client.tsx`:

```tsx
<SiteActivationWidget
  data={data.siteActivation.map((r) => ({
    hostname: r.hostname ?? r.site_domain ?? "unknown",
    phiaShown: r.phia_shown_count ?? 0,
    phiaClickedWithBookmark: r.phia_clicked_count ?? 0,
    activationRate: r.activation_rate ?? 0,
  }))}
  platform={data.siteActivation[0]?.platform}
/>
```

Replicate this pattern for the remaining widgets.

---

## 4 Table → Widget mapping

| Supabase table             | Widget(s)                              | Required columns                                                                    |
| -------------------------- | -------------------------------------- | ----------------------------------------------------------------------------------- |
| `site_activation`          | SiteActivationWidget                   | `hostname`, `phia_shown_count`, `phia_clicked_count`, `activation_rate`, `platform` |
| `onboarding_funnel`        | OnboardingPermissionsWidget            | `date`, `overall_conversion_rate`, `step1_users`, `step2_users`                     |
| `safari_extension_metrics` | PhiaClicksWidget                       | `metric_name`, `unique_users`, `total_events`                                       |
| `daily_metrics`            | HeartbeatWidget                        | `date`, `unique_users`, `total_events`, filter `metric_type = 'heartbeat'`          |
| `cohort_retention`         | RetentionWidget & CohortRetentionTable | `week_number`, `cohort_start_date`, `retention_rate`, `cohort_size`                 |

---

## 5 Adding new metrics

1. Add the SQL table in Supabase migrations.
2. Create a typed interface and fetcher in `actions.ts`.
3. Consume the data in the relevant component via props.
4. Update `fetchMetricsDashboard()` if the data is always needed; otherwise call your new fetcher ad-hoc.

---

## 6 Testing checklist

- Run `pnpm dev` and open `/metrics` – ensure no mock numbers remain.
- Turn on network throttling to verify loading states.
- Sign in/out flow still guards the route.
- Verify authorized RLS access: logged-out users should not receive any data (handled by server-side redirect).

---

Happy analysing! 🎉

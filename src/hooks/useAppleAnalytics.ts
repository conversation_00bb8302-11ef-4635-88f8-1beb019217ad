import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/supabase/client/client';

// Types for Apple Analytics data
export interface AppleAnalyticsRecord {
  unique_id: string;
  app_id: string;
  report_date: string;
  event_type: 'install' | 'uninstall';
  event_subtype: string | null;
  app_name: string;
  app_version: string;
  device_type: string;
  platform_version: string;
  source_type: string;
  source_app: string | null;
  campaign_name: string | null;
  page_type: string;
  page_title: string;
  territory: string;
  event_count: number;
  unique_device_count: number;
  download_date: string | null;
  processed_at: string;
  data_source: string;
}

export interface AppleDailySummary {
  report_date: string;
  daily_sessions: number;
  daily_active_users: number;
  daily_duration_seconds: number;
  daily_duration_hours: string;
  avg_session_duration_minutes: string;
  daily_installs: number;
  daily_install_devices: number;
  daily_uninstalls: number;
  daily_uninstall_devices: number;
  net_user_growth: number;
  sessions_per_active_user: number;
}

export interface AppleSessionAnalytics {
  report_date: string;
  app_id: string;
  app_name: string;
  app_version: string;
  device_type: string;
  platform_version: string;
  territory: string;
  total_sessions: number;
  total_duration_seconds: number;
  total_active_users: number;
  avg_session_duration_seconds: number;
  sessions_per_user: number;
  avg_session_duration_minutes: string;
  total_duration_hours: string;
}

export interface AppleAnalyticsMetrics {
  totalInstalls: number;
  totalUninstalls: number;
  netInstalls: number;
  firstTimeDownloads: number;
  updates: number;
  restores: number;
  redownloads: number;
  topTerritories: Array<{ territory: string; installs: number; uninstalls: number }>;
  topSourceTypes: Array<{ source_type: string; installs: number; uninstalls: number }>;
  topSourceApps: Array<{ source_app: string; installs: number; uninstalls: number }>;
  dailyTrends: Array<{ date: string; installs: number; uninstalls: number }>;
  deviceBreakdown: Array<{ device_type: string; installs: number; uninstalls: number }>;
  versionBreakdown: Array<{ app_version: string; installs: number; uninstalls: number }>;
  platformVersions: Array<{ platform_version: string; installs: number; uninstalls: number }>;
}

export interface AppleAnalyticsData {
  records: AppleAnalyticsRecord[];
  metrics: AppleAnalyticsMetrics;
  dailySummary: AppleDailySummary[];
  sessionAnalytics: AppleSessionAnalytics[];
  dateRange: {
    earliest: string;
    latest: string;
  };
}

interface UseAppleAnalyticsOptions {
  timeRange?: string;
  startDate?: string;
  endDate?: string;
  autoFetch?: boolean;
}

interface UseAppleAnalyticsReturn {
  data: AppleAnalyticsData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Helper function to process raw data into metrics
function processAppleAnalyticsData(records: AppleAnalyticsRecord[]): AppleAnalyticsMetrics {
  const installRecords = records.filter(r => r.event_type === 'install');
  const uninstallRecords = records.filter(r => r.event_type === 'uninstall');

  // Basic totals
  const totalInstalls = installRecords.reduce((sum, r) => sum + r.event_count, 0);
  const totalUninstalls = uninstallRecords.reduce((sum, r) => sum + r.event_count, 0);
  const netInstalls = totalInstalls - totalUninstalls;

  // Install subtypes
  const firstTimeDownloads = installRecords
    .filter(r => r.event_subtype === 'first_time_download')
    .reduce((sum, r) => sum + r.event_count, 0);

  const updates = installRecords
    .filter(r => r.event_subtype === 'manual_update' || r.event_subtype === 'auto_update')
    .reduce((sum, r) => sum + r.event_count, 0);

  const restores = installRecords
    .filter(r => r.event_subtype === 'restore')
    .reduce((sum, r) => sum + r.event_count, 0);

  const redownloads = installRecords
    .filter(r => r.event_subtype === 'redownload')
    .reduce((sum, r) => sum + r.event_count, 0);

  // Territory breakdown
  const territoryMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!territoryMap.has(r.territory)) {
      territoryMap.set(r.territory, { installs: 0, uninstalls: 0 });
    }
    const entry = territoryMap.get(r.territory)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const topTerritories = Array.from(territoryMap.entries())
    .map(([territory, counts]) => ({ territory, ...counts }))
    .sort((a, b) => b.installs - a.installs)
    .slice(0, 10);

  // Source type breakdown
  const sourceTypeMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!sourceTypeMap.has(r.source_type)) {
      sourceTypeMap.set(r.source_type, { installs: 0, uninstalls: 0 });
    }
    const entry = sourceTypeMap.get(r.source_type)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const topSourceTypes = Array.from(sourceTypeMap.entries())
    .map(([source_type, counts]) => ({ source_type, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Source app breakdown (only for app_referrer)
  const sourceAppMap = new Map<string, { installs: number; uninstalls: number }>();
  records.filter(r => r.source_app).forEach(r => {
    const sourceApp = r.source_app!;
    if (!sourceAppMap.has(sourceApp)) {
      sourceAppMap.set(sourceApp, { installs: 0, uninstalls: 0 });
    }
    const entry = sourceAppMap.get(sourceApp)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const topSourceApps = Array.from(sourceAppMap.entries())
    .map(([source_app, counts]) => ({ source_app, ...counts }))
    .sort((a, b) => b.installs - a.installs)
    .slice(0, 10);

  // Daily trends
  const dailyMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!dailyMap.has(r.report_date)) {
      dailyMap.set(r.report_date, { installs: 0, uninstalls: 0 });
    }
    const entry = dailyMap.get(r.report_date)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const dailyTrends = Array.from(dailyMap.entries())
    .map(([date, counts]) => ({ date, ...counts }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Device breakdown
  const deviceMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!deviceMap.has(r.device_type)) {
      deviceMap.set(r.device_type, { installs: 0, uninstalls: 0 });
    }
    const entry = deviceMap.get(r.device_type)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const deviceBreakdown = Array.from(deviceMap.entries())
    .map(([device_type, counts]) => ({ device_type, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Version breakdown
  const versionMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!versionMap.has(r.app_version)) {
      versionMap.set(r.app_version, { installs: 0, uninstalls: 0 });
    }
    const entry = versionMap.get(r.app_version)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const versionBreakdown = Array.from(versionMap.entries())
    .map(([app_version, counts]) => ({ app_version, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Platform version breakdown
  const platformVersionMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!platformVersionMap.has(r.platform_version)) {
      platformVersionMap.set(r.platform_version, { installs: 0, uninstalls: 0 });
    }
    const entry = platformVersionMap.get(r.platform_version)!;
    if (r.event_type === 'install') {
      entry.installs += r.event_count;
    } else {
      entry.uninstalls += r.event_count;
    }
  });

  const platformVersions = Array.from(platformVersionMap.entries())
    .map(([platform_version, counts]) => ({ platform_version, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  return {
    totalInstalls,
    totalUninstalls,
    netInstalls,
    firstTimeDownloads,
    updates,
    restores,
    redownloads,
    topTerritories,
    topSourceTypes,
    topSourceApps,
    dailyTrends,
    deviceBreakdown,
    versionBreakdown,
    platformVersions,
  };
}

export function useAppleAnalytics(options: UseAppleAnalyticsOptions = {}): UseAppleAnalyticsReturn {
  const { timeRange = 'all', startDate, endDate, autoFetch = true } = options;

  const [data, setData] = useState<AppleAnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAppleAnalytics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      let query = supabase
        .from('apple_analytics_data')
        .select('*')
        .order('report_date', { ascending: false });

      // Apply date filters based on timeRange or explicit dates
      if (startDate && endDate) {
        query = query.gte('report_date', startDate).lte('report_date', endDate);
      } else if (timeRange !== 'all') {
        const now = new Date();
        let filterDate: Date;

        switch (timeRange) {
          case '7d':
            filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            filterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90d':
            filterDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            filterDate = new Date(0); // All time
        }

        if (timeRange !== 'all') {
          query = query.gte('report_date', filterDate.toISOString().split('T')[0]);
        }
      }

      const { data: records, error: fetchError } = await query;

      if (fetchError) {
        throw new Error(`Failed to fetch Apple Analytics data: ${fetchError.message}`);
      }

      // Fetch daily summary data
      const { data: dailySummary, error: summaryError } = await supabase
        .from('apple_daily_summary')
        .select('*')
        .order('report_date', { ascending: true });

      if (summaryError) {
        throw new Error(`Failed to fetch daily summary: ${summaryError.message}`);
      }

      // Fetch session analytics data
      const { data: sessionAnalytics, error: sessionError } = await supabase
        .from('apple_session_analytics')
        .select('*')
        .order('report_date', { ascending: true });

      if (sessionError) {
        throw new Error(`Failed to fetch session analytics: ${sessionError.message}`);
      }

      if (!records || records.length === 0) {
        setData({
          records: [],
          metrics: {
            totalInstalls: 0,
            totalUninstalls: 0,
            netInstalls: 0,
            firstTimeDownloads: 0,
            updates: 0,
            restores: 0,
            redownloads: 0,
            topTerritories: [],
            topSourceTypes: [],
            topSourceApps: [],
            dailyTrends: [],
            deviceBreakdown: [],
            versionBreakdown: [],
            platformVersions: [],
          },
          dailySummary: dailySummary || [],
          sessionAnalytics: sessionAnalytics || [],
          dateRange: { earliest: '', latest: '' }
        });
        return;
      }

      // Process the data
      const metrics = processAppleAnalyticsData(records);

      // Calculate date range
      const dates = records.map(r => r.report_date).sort();
      const dateRange = {
        earliest: dates[0] || '',
        latest: dates[dates.length - 1] || ''
      };

      setData({
        records,
        metrics,
        dailySummary: dailySummary || [],
        sessionAnalytics: sessionAnalytics || [],
        dateRange
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch Apple Analytics data';
      setError(errorMessage);
      console.error('Error fetching Apple Analytics data:', err);
    } finally {
      setLoading(false);
    }
  }, [timeRange, startDate, endDate]);

  useEffect(() => {
    if (autoFetch) {
      fetchAppleAnalytics();
    }
  }, [fetchAppleAnalytics, autoFetch]);

  return {
    data,
    loading,
    error,
    refetch: fetchAppleAnalytics,
  };
}

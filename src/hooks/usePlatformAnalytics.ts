import { useState, useEffect, useCallback } from 'react';
import { PlatformAnalytics } from '@/lib/analytics-utils';

export interface TransactionFilters {
  platform?: string;
  merchant?: string;
  status?: string;
  network?: string;
  transactionType?: string;
  startDate?: string;
  endDate?: string;
}

interface UsePlatformAnalyticsReturn {
  data?: PlatformAnalytics;
  loading: boolean;
  error?: string;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for fetching platform analytics data with dynamic time range, date range, and transaction filtering support
 */
export function usePlatformAnalytics(
  platform: string,
  timeRange: string = 'all',
  startDate?: string,
  endDate?: string,
  transactionFilters?: TransactionFilters
): UsePlatformAnalyticsReturn {
  const [data, setData] = useState<PlatformAnalytics | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>(undefined);

  const fetchAnalytics = useCallback(async () => {
    setLoading(true);
    setError(undefined);

    try {
      // Build query parameters
      const params = new URLSearchParams({
        platform,
        timeRange,
      });

      if (startDate) {
        params.append('startDate', startDate);
      }
      if (endDate) {
        params.append('endDate', endDate);
      }

      // Add transaction filter parameters
      if (transactionFilters) {
        if (transactionFilters.merchant) {
          params.append('merchant', transactionFilters.merchant);
        }
        if (transactionFilters.status) {
          params.append('status', transactionFilters.status);
        }
        if (transactionFilters.network) {
          params.append('network', transactionFilters.network);
        }
        if (transactionFilters.transactionType) {
          params.append('transactionType', transactionFilters.transactionType);
        }
        // Note: platform filter from transactionFilters will override the main platform parameter
        if (transactionFilters.platform) {
          params.set('platform', transactionFilters.platform);
        }
        // startDate/endDate from transactionFilters override the main parameters
        if (transactionFilters.startDate) {
          params.set('startDate', transactionFilters.startDate);
        }
        if (transactionFilters.endDate) {
          params.set('endDate', transactionFilters.endDate);
        }
      }

      const response = await fetch(`/api/analytics/platform?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      setData(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch analytics data';
      setError(errorMessage);
      console.error('Error fetching platform analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [platform, timeRange, startDate, endDate, transactionFilters]);

  // Fetch data when platform, timeRange, or date range changes
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return {
    data,
    loading,
    error,
    refetch: fetchAnalytics,
  };
}

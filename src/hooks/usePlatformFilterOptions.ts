import { useState, useEffect } from 'react';

export interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface PlatformFilterOptionsData {
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
}

interface UsePlatformFilterOptionsReturn {
  data: PlatformFilterOptionsData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function usePlatformFilterOptions(platform: 'strackr' | 'shopmy'): UsePlatformFilterOptionsReturn {
  const [data, setData] = useState<PlatformFilterOptionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPlatformFilterOptions = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/transactions/filters?platform=${platform}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      setData({
        merchants: result.merchants || [],
        statuses: result.statuses || [],
        networks: result.networks || [],
        transactionTypes: result.transactionTypes || []
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch platform filter options';
      setError(errorMessage);
      console.error(`Error fetching ${platform} filter options:`, err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlatformFilterOptions();
  }, [platform]);

  return {
    data,
    loading,
    error,
    refetch: fetchPlatformFilterOptions,
  };
}
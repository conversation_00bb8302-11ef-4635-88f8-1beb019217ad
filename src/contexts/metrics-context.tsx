"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface MetricsContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  getPasswordForActiveTab: () => string | null;
}

const MetricsContext = createContext<MetricsContextType | undefined>(undefined);

export function MetricsProvider({ children }: { children: ReactNode }) {
  const [activeTab, setActiveTab] = useState("extension");

  const getPasswordForActiveTab = () => {
    switch (activeTab) {
      case "extension":
        return "NQ>Z4C{j->";
      case "mobile":
        return "h:{/Aov042";
      default:
        return null;
    }
  };

  return (
    <MetricsContext.Provider value={{ activeTab, setActiveTab, getPasswordForActiveTab }}>
      {children}
    </MetricsContext.Provider>
  );
}

export function useMetrics() {
  const context = useContext(MetricsContext);
  if (context === undefined) {
    throw new Error("useMetrics must be used within a MetricsProvider");
  }
  return context;
}

// Database types for Supabase tables
export interface NormalizedTransaction {
  id: string;
  transaction_id: string;
  platform: string;
  source_transaction_id: string;
  currency: string;
  order_amount: number;
  commission_amount: number;
  final_order_amount?: number;
  final_commission_amount?: number;
  order_id: string;
  customer_id?: string;
  transaction_date: string;
  created_date: string;
  network_name: string;
  merchant_name: string;
  merchant_id?: string;
  connection_name?: string;
  status: string;
  transaction_type?: string;
  decline_reason?: string;
  channel_name?: string;
  custom_fields?: Record<string, any>;
  comments?: string;
  last_updated: string;
  created_at?: string;
  updated_at?: string;
}

export interface DailyPlatformSummary {
  platform?: string;
  date?: string;
  total_transactions?: number;
  confirmed_transactions?: number;
  pending_transactions?: number;
  declined_transactions?: number;
  total_order_amount?: number;
  total_commission_amount?: number;
  confirmed_commission?: number;
}

export interface MerchantPerformance {
  platform?: string;
  merchant_name?: string;
  network_name?: string;
  total_transactions?: number;
  confirmed_transactions?: number;
  confirmation_rate?: number;
  total_commission?: number;
  avg_commission?: number;
  last_transaction_date?: string;
}

export interface TransactionSummary {
  platform?: string;
  transaction_day?: string;
  status?: string;
  currency?: string;
  transaction_count?: number;
  total_order_amount?: number;
  total_commission_amount?: number;
  avg_order_amount?: number;
  avg_commission_amount?: number;
}

// Transformed data type for the spreadsheet display
export interface TransactionDisplayData {
  id: string;
  date: string;
  platform: string;
  merchant: string;
  network: string;
  status: string;
  order_amount: number;
  commission_amount: number;
  currency: string;
  transaction_type?: string;
  order_id: string;
  customer_id?: string;
}

// API response types
export interface TransactionApiResponse {
  data: NormalizedTransaction[];
  count?: number;
  error?: string;
}

export interface SummaryApiResponse {
  data: DailyPlatformSummary[] | MerchantPerformance[] | TransactionSummary[];
  count?: number;
  error?: string;
}

export interface MerchantData {
  advertiserId: string;
  networkId: string;
  networkName: string;
  device: string;
  commissionRateMax: string;
  networkRank: string;
  evergreenUrl: string;
  evergreenUrlOverride: string | null;
  cookieDurationHours: string | null;
  domain: string;
  name: string;
  website: string;
}

export interface MerchantUrl {
  id: string;
  merchantId: string;
  url: string;
  platform: number;
  phiaId: string;
  searchId: string;
  productName: string;
  productColor: string;
  directLink: string;
  createdAt: string;
}

export interface CreateMerchantUrlResponse {
  merchantUrl: MerchantUrl;
  availableMerchants: MerchantData[];
}

export interface MerchantOverride {
  domain: string;
  isBlacklisted: boolean;
  preferredNetworkId: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export type CreateMerchantUrlRequest = {
  url: string;
};

export type CreateMerchantOverrideRequest = {
  domain: string;
  isBlacklisted: boolean;
  preferredNetworkId: string;
};

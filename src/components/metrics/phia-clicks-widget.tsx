"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";

// Mock data based on the third image - <PERSON><PERSON> clicks widget
const phiaClicksData = {
  title: "Weekly ACTIVE Safari Extension Users",
  subtitle: "Weekly Safari Extension Users",
  totalClicks: "13.74K",
  metric: "<PERSON><PERSON> clicked with bookmark [Distinct Count of phia_id]",
  platform: "IOS_SAFARI_EXTENSION",
  breakdown: [
    { event: "<PERSON><PERSON> clicked with bookmark [Distinct Count of phia_id]", value: 13740 }
  ]
};

export interface PhiaClicksWidgetProps {
  data?: {
    totalEvents: number;
    uniqueUsers: number;
    metricName: string;
    platform: string;
  }[];
}

export function PhiaClicksWidget({ data }: PhiaClicksWidgetProps) {
  // Use real data if available, otherwise fall back to mock data
  const totalEvents = data && data.length > 0 ? data[0].totalEvents : 13740;
  const uniqueUsers = data && data.length > 0 ? data[0].uniqueUsers : 0;
  const metricName = data && data.length > 0 ? data[0].metricName : phiaClicksData.metric;
  const platform = data && data.length > 0 ? data[0].platform : phiaClicksData.platform;

  // Format the total events for display
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(2)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(2)}K`;
    return num.toString();
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">{phiaClicksData.title}</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {phiaClicksData.subtitle}
          </p>
        </div>

      </CardHeader>
      <CardContent>
        <div className="space-y-6">


          {/* Large metric display */}
          <div className="text-center py-8 bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg border">
            <div className="text-5xl font-bold text-primary mb-2 tracking-tight">
              {formatNumber(totalEvents)}
            </div>
            <div className="text-sm text-muted-foreground max-w-xs mx-auto">
              {metricName}
            </div>
          </div>

          {/* Platform filter indicator */}
          <div className="flex items-center justify-center">
            <div className="inline-flex items-center gap-2 px-3 py-2 bg-muted/50 border rounded-full text-sm">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="text-muted-foreground">Platform:</span>
              <span className="font-medium text-foreground">{phiaClicksData.platform}</span>
            </div>
          </div>

          {/* Breakdown section */}
          <div className="space-y-3">
            <div className="text-sm font-medium text-muted-foreground">Breakdown</div>

            {phiaClicksData.breakdown.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-muted/30 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-primary rounded"></div>
                  <span className="text-sm text-foreground">{item.event}</span>
                </div>
                <div className="text-lg font-semibold text-foreground">
                  {item.value.toLocaleString()}
                </div>
              </div>
            ))}
          </div>

          {/* Additional metrics */}
          <div className="pt-4 border-t border-border">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-3 bg-muted/20 rounded-lg">
                <div className="text-lg font-semibold text-foreground">13.74K</div>
                <div className="text-xs text-muted-foreground">This Week</div>
              </div>
              <div className="p-3 bg-muted/20 rounded-lg">
                <div className="text-lg font-semibold text-foreground">12.89K</div>
                <div className="text-xs text-muted-foreground">Last Week</div>
              </div>
              <div className="p-3 bg-muted/20 rounded-lg">
                <div className="text-lg font-semibold text-green-600 dark:text-green-400">+6.6%</div>
                <div className="text-xs text-muted-foreground">Change</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

// Mock data based on the first image
const siteActivationData = [
  { hostname: "www.amazon.com", phiaShown: 443554, phiaClickedWithBookmark: 19196, activationRate: 4.14 },
  { hostname: "www.nordstrom.com", phiaShown: 158622, phiaClickedWithBookmark: 10139, activationRate: 6.82 },
  { hostname: "www.quince.com", phiaShown: 156399, phiaClickedWithBookmark: 8896, activationRate: 5.93 },
  { hostname: "www.aritzia.com", phiaShown: 118965, phiaClickedWithBookmark: 6978, activationRate: 5.86 },
  { hostname: "www.jcrew.com", phiaShown: 109161, phiaClickedWithBookmark: 4598, activationRate: 4.21 },
  { hostname: "www.thereformation.com", phiaShown: 103638, phiaClickedWithBookmark: 10692, activationRate: 10.37 },
  { hostname: "www.revolve.com", phiaShown: 98122, phiaClickedWithBookmark: 9167, activationRate: 9.52 },
  { hostname: "skims.com", phiaShown: 91274, phiaClickedWithBookmark: 3190, activationRate: 3.49 },
  { hostname: "www.target.com", phiaShown: 88582, phiaClickedWithBookmark: 2917, activationRate: 3.29 },
  { hostname: "www.etsy.com", phiaShown: 83490, phiaClickedWithBookmark: 4564, activationRate: 5.44 },
  { hostname: "www.ebay.com", phiaShown: 83866, phiaClickedWithBookmark: 8649, activationRate: 10.31 },
  { hostname: "www2.hm.com", phiaShown: 83617, phiaClickedWithBookmark: 2574, activationRate: 3.07 }
];

function calculateTotals(dataArray: typeof siteActivationData) {
  const totalPhiaShown = dataArray.reduce((sum, item) => sum + item.phiaShown, 0);
  const totalPhiaClicked = dataArray.reduce((sum, item) => sum + item.phiaClickedWithBookmark, 0);
  const overallActivationRate = (totalPhiaClicked / totalPhiaShown) * 100;
  return { totalPhiaShown, totalPhiaClicked, overallActivationRate };
}

export interface SiteActivationWidgetProps {
  data?: {
    hostname: string | null;
    phiaShown: number;
    phiaClickedWithBookmark: number;
    activationRate: number;
  }[];
  platform?: string;
}

export function SiteActivationWidget({ data, platform = "IOS_SAFARI_EXTENSION" }: SiteActivationWidgetProps) {
  const mergedData = data ?? siteActivationData;
  const { totalPhiaShown, totalPhiaClicked, overallActivationRate } = calculateTotals(mergedData as any);

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">Site Activation Rate by Domain</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Platform: {platform}
          </p>
        </div>

      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary row */}
          <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
            <span className="font-medium text-foreground">Overall</span>
            <div className="flex gap-8 text-sm text-muted-foreground">
              <span>{totalPhiaShown.toLocaleString()}</span>
              <span>{totalPhiaClicked.toLocaleString()}</span>
              <span className="font-medium text-foreground">{overallActivationRate.toFixed(2)}%</span>
            </div>
          </div>

          {/* Site Activation Data */}
          <div className="space-y-4">
            {/* Header */}
            <div className="grid grid-cols-4 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
              <span>hostname</span>
              <span className="text-right">Phia Shown</span>
              <span className="text-right">Phia clicked with bookmark</span>
              <span className="text-right">Activation Rate</span>
            </div>

            {/* Data rows */}
            <div className="space-y-1 max-h-80 overflow-y-auto">
              {mergedData.map((item, index) => (
                <div key={index} className="grid grid-cols-4 gap-4 text-sm py-3 hover:bg-muted/30 rounded-md transition-colors">
                  <a href={`https://${item.hostname || 'unknown'}`} className="text-primary hover:underline truncate font-medium">
                    {item.hostname || 'Unknown'}
                  </a>
                  <span className="text-right text-muted-foreground">{item.phiaShown.toLocaleString()}</span>
                  <span className="text-right text-muted-foreground">{item.phiaClickedWithBookmark.toLocaleString()}</span>
                  <span className="text-right font-semibold text-foreground">{item.activationRate.toFixed(2)}%</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

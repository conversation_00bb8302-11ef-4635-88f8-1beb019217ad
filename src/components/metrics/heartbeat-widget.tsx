"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

// Mock data based on the fourth image - Heartbeat widget
const heartbeatData = {
  title: "Safari Extension Users Since Launch",
  subtitle: "Weekly Safari Extension Users",
  totalUsers: {
    ios: "91.46K",
    mac: "8,391",
    win: "4,117"
  },
  breakdown: [
    { os: "ios", label: "iOS", value: 91460, color: "bg-blue-500" },
    { os: "mac", label: "macOS", value: 8391, color: "bg-red-500" },
    { os: "win", label: "Windows", value: 4117, color: "bg-cyan-500" },
    { os: "cros", label: "Chrome OS", value: 91, color: "bg-gray-400" },
    { os: "linux", label: "Linux", value: 22, color: "bg-gray-300" }
  ]
};

export interface HeartbeatWidgetProps {
  data?: {
    date: string;
    uniqueUsers: number;
    totalEvents: number;
    eventName: string;
  }[];
}

const totalUsers = heartbeatData.breakdown.reduce((sum, item) => sum + item.value, 0);

export function HeartbeatWidget({ data }: HeartbeatWidgetProps) {
  // Use real data if available, otherwise fall back to mock data
  const totalUniqueUsers = data && data.length > 0 ? data.reduce((sum, row) => sum + row.uniqueUsers, 0) : totalUsers;
  const totalEvents = data && data.length > 0 ? data.reduce((sum, row) => sum + row.totalEvents, 0) : 0;

  // Format numbers for display
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(2)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(2)}K`;
    return num.toLocaleString();
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">{heartbeatData.title}</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {heartbeatData.subtitle}
          </p>
        </div>

      </CardHeader>
      <CardContent>
        <div className="space-y-6">


          {/* Large metrics display */}
          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="p-4 bg-primary/10 border border-primary/20 rounded-lg">
              <div className="text-3xl font-bold text-primary mb-1">
                {formatNumber(totalUniqueUsers)}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Total Unique Users</div>
            </div>
            <div className="p-4 bg-secondary/10 border border-secondary/20 rounded-lg">
              <div className="text-3xl font-bold text-secondary-foreground mb-1">
                {formatNumber(totalEvents)}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Total Events</div>
            </div>
          </div>

          {/* Heartbeat Data breakdown */}
          <div className="space-y-3">
            <div className="text-sm font-medium text-muted-foreground">Heartbeat Events Breakdown</div>

            <div className="space-y-2">
              {data && data.length > 0 ? data.map((item, index) => {
                return (
                  <div key={index} className="flex items-center justify-between p-3 hover:bg-muted/30 rounded-lg transition-colors border border-transparent hover:border-border">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 rounded bg-primary"></div>
                      <span className="text-sm font-medium text-foreground">{new Date(item.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-muted-foreground">Users: {item.uniqueUsers.toLocaleString()}</span>
                      <span className="text-sm font-semibold min-w-[60px] text-right text-foreground">
                        Events: {item.totalEvents.toLocaleString()}
                      </span>
                    </div>
                  </div>
                );
              }) : (
                <div className="text-center text-muted-foreground py-4">
                  No heartbeat data available
                </div>
              )}
            </div>
          </div>

          {/* Total summary */}
          <div className="pt-4 border-t border-border">
            <div className="flex justify-between items-center p-4 bg-muted/30 border rounded-lg">
              <span className="font-medium text-foreground">Total Unique Users</span>
              <span className="text-lg font-bold text-foreground">
                {formatNumber(totalUniqueUsers)}
              </span>
            </div>
          </div>

          {/* Heartbeat metric info */}
          <div className="text-xs text-muted-foreground text-center bg-muted/20 p-2 rounded">
            Heartbeat events from Safari Extension users
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import React, { useRef, useState, useEffect } from "react";

interface MixpanelEmbedCardProps {
  /**
   * Title displayed at the top of the card.
   */
  title: string;
  /**
   * Public or shared Mixpanel embed URL.
   * Example: https://mixpanel.com/s/3scdw
   */
  src: string;
  /**
   * Height of the iframe in pixels. Defaults to 600.
   */
  height?: number;
  /**
   * If true, the original src will be passed through Iframely using the public or project key.
   * Defaults to false, but automatically enabled when NEXT_PUBLIC_IFRAMELY_KEY is present.
   */
  wrapWithIframely?: boolean;
}

// Quick helper to fetch HEAD for debugging iframe embeddability
async function checkEmbed(url: string) {
  try {
    const res = await fetch(url, { method: "HEAD" });
    console.groupCollapsed(`[MixpanelEmbedCard] HEAD ${url}`);
    console.log("status", res.status, res.statusText);
    const xfo = res.headers.get("x-frame-options");
    const csp = res.headers.get("content-security-policy");
    console.log("x-frame-options", xfo);
    console.log("content-security-policy", csp);
    console.groupEnd();
  } catch (err) {
    console.error(`[MixpanelEmbedCard] HEAD request failed`, err);
  }
}

/**
 * Renders a Mixpanel report inside an iframe, wrapped in a Card.
 * The parent page must ensure the user is authenticated before
 * rendering this component.
 */
export function MixpanelEmbedCard({ title, src, height = 600, wrapWithIframely }: MixpanelEmbedCardProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [status, setStatus] = useState<"loading" | "loaded" | "error">("loading");

  const iframelyKey = typeof window !== "undefined" ? process.env.NEXT_PUBLIC_IFRAMELY_KEY : undefined;
  const iframelyBase = typeof window !== "undefined" ? process.env.NEXT_PUBLIC_IFRAMELY_BASE : undefined;
  const iframelyKeyParam = typeof window !== "undefined" ? process.env.NEXT_PUBLIC_IFRAMELY_KEY_PARAM : undefined;

  const shouldWrap = wrapWithIframely ?? Boolean(iframelyKey);

  const buildIframelySrc = () => {
    const base = iframelyBase || "https://cdn.iframe.ly/api/iframe";
    const keyParam = iframelyKeyParam || "key"; // some accounts expect api_key
    const params = new URLSearchParams({
      url: src,
      [keyParam]: iframelyKey ?? "demo",
      omit_script: "1",
    });
    return `${base}?${params.toString()}`;
  };

  const resolvedSrc = shouldWrap ? buildIframelySrc() : src;

  useEffect(() => {
    if (!iframeRef.current) return;

    const handleLoad = () => {
      console.info(`[MixpanelEmbedCard] iframe loaded: ${src}`);
      setStatus("loaded");
    };

    const handleError = () => {
      console.error(`[MixpanelEmbedCard] iframe failed to load: ${src}`);
      setStatus("error");
    };

    const node = iframeRef.current;
    node.addEventListener("load", handleLoad);
    node.addEventListener("error", handleError);

    return () => {
      node.removeEventListener("load", handleLoad);
      node.removeEventListener("error", handleError);
    };
  }, [src]);

  // Fire one-time HEAD check on mount (client only)
  useEffect(() => {
    if (typeof window !== "undefined") {
      checkEmbed(src);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {/* Wrapper div needed by Mixpanel to maintain aspect ratio/positioning */}
        <div
          style={{ position: "relative", width: "100%", height: `${height}px`, left: 0 }}
        >
          <iframe
            src={resolvedSrc}
            title={title}
            style={{ position: "absolute", top: 0, left: 0, width: "100%", height: "100%", border: 0 }}
            allow="fullscreen *; clipboard-write"
            allowFullScreen
            referrerPolicy="no-referrer"
            suppressHydrationWarning
            ref={iframeRef}
          />
        </div>

        {status === "error" && (
          <div className="p-4 text-sm text-red-500">
            Failed to load Mixpanel embed. Check browser console for details.
          </div>
        )}
      </CardContent>
    </Card>
  );
}

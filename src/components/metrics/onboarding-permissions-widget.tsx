"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

// Mock data based on the image - onboarding permissions chart
const onboardingData = {
  title: "Enabled Permissions % First-Time Run",
  metric: "% Users that gave ALL permissions for Safari Onboarding",
  timeRange: "Jun 24 - Jul 1",
  chartData: [
    { date: "Jun 24", percentage: 32.0 },
    { date: "Jun 25", percentage: 31.5 },
    { date: "Jun 26", percentage: 33.2 },
    { date: "Jun 27", percentage: 34.1 },
    { date: "Jun 28", percentage: 31.8 },
    { date: "Jun 29", percentage: 32.9 },
    { date: "Jun 30", percentage: 33.5 },
    { date: "Jul 1", percentage: 32.7 }
  ],
  currentValue: 32.56,
  trend: "up"
};

export interface OnboardingPermissionsWidgetProps {
  data?: {
    date: string;
    conversionRate: number;
    step1Users: number;
    step2Users: number;
    overallConversionRate: number;
  }[];
}

export function OnboardingPermissionsWidget({ data }: OnboardingPermissionsWidgetProps) {
  // Use real data if available, otherwise fall back to mock data
  const chartData = data ? data.map((row, index) => ({
    date: new Date(row.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    percentage: row.conversionRate
  })) : onboardingData.chartData;

  const currentValue = data && data.length > 0 ? data[data.length - 1].conversionRate : onboardingData.currentValue;
  const maxValue = Math.max(...chartData.map(d => d.percentage));
  const minValue = Math.min(...chartData.map(d => d.percentage));

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-base font-medium">{onboardingData.title}</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            {onboardingData.timeRange}
          </p>
        </div>

      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Value Display */}
          <div className="text-center p-4 bg-muted/20 rounded-lg border">
            <div className="text-3xl font-bold text-primary mb-2">
              {currentValue.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">
              Current Conversion Rate
            </div>
          </div>

          {/* Onboarding Data */}
          <div className="space-y-4">
            {/* Data table */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <div className="w-3 h-3 bg-primary rounded"></div>
                <span className="font-medium text-foreground">Onboarding Conversion Data</span>
              </div>

              <div className="grid grid-cols-3 gap-4 text-xs font-medium text-muted-foreground border-b border-border pb-3">
                <span>Date</span>
                <span className="text-right">Step 1 Users</span>
                <span className="text-right">Conversion Rate</span>
              </div>

              {chartData.map((point, index) => (
                <div key={index} className="grid grid-cols-3 gap-4 text-sm py-2 hover:bg-muted/30 rounded-md transition-colors">
                  <span className="text-foreground font-medium">{point.date}</span>
                  <span className="text-right text-muted-foreground">
                    {data && data[index] ? data[index].step1Users.toLocaleString() : 'N/A'}
                  </span>
                  <span className="text-right">
                    <span className="bg-primary/20 text-primary px-2 py-1 rounded text-center font-medium">
                      {point.percentage.toFixed(1)}%
                    </span>
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

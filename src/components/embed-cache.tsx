"use client";

import React, { useEffect, useState, createContext, useContext, ReactNode } from "react";

// Embed configurations
export const EMBED_CONFIGS = {
  "metrics-extension": {
    url: "https://cdn.iframe.ly/api/iframe?url=https%3A%2F%2Fmixpanel.com%2Fp%2FHki7yXwHXeiAmjSn9ATMk2%3Fembed%3Dtrue%26passcode%3DNQ%253EZ4C%257Bj-%253E&key=42a8c3dc9eef44e8664fef954e335cf7",
    title: "Extension Metrics"
  },
  "metrics-mobile": {
    url: "https://cdn.iframe.ly/api/iframe?url=https%3A%2F%2Fmixpanel.com%2Fp%2FXtCFBJJMXFyuwfJurB7Pq3%3Fembed%3Dtrue%26passcode%3Dh%253A%257B%252FAov042&key=42a8c3dc9eef44e8664fef954e335cf7",
    title: "Mobile Metrics"
  },
  "affiliates": {
    url: "https://cdn.iframe.ly/api/iframe?url=https%3A%2F%2Fmixpanel.com%2Fp%2FDmnJwXfHm8joGs5ui6qcM2%3Fembed%3Dtrue%26passcode%3DgFYA%253C8ZsPK&key=42a8c3dc9eef44e8664fef954e335cf7",
    title: "Affiliates"
  }
} as const;

type EmbedId = keyof typeof EMBED_CONFIGS;

interface EmbedCacheContextType {
  preloadedEmbeds: Set<string>;
  isEmbedPreloaded: (embedId: string) => boolean;
  getEmbedUrl: (embedId: string) => string | null;
}

const EmbedCacheContext = createContext<EmbedCacheContextType | undefined>(undefined);

export function EmbedCacheProvider({ children }: { children: ReactNode }) {
  const [preloadedEmbeds, setPreloadedEmbeds] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Start preloading after initial page load
    const timer = setTimeout(() => {
      Object.entries(EMBED_CONFIGS).forEach(([id, config]) => {
        preloadEmbed(id, config.url);
      });
    }, 1500); // 1.5 second delay

    return () => clearTimeout(timer);
  }, []);

  const preloadEmbed = (embedId: string, url: string) => {
    // Create hidden iframe for preloading
    const iframe = document.createElement('iframe');
    iframe.src = url;
    iframe.style.cssText = `
      position: absolute;
      left: -9999px;
      top: -9999px;
      width: 1px;
      height: 1px;
      visibility: hidden;
      pointer-events: none;
      border: none;
    `;
    iframe.setAttribute('aria-hidden', 'true');
    iframe.setAttribute('tabindex', '-1');
    iframe.allow = "fullscreen *; autoplay; camera; microphone; geolocation;";

    const handleLoad = () => {
      console.log(`✅ Preloaded: ${EMBED_CONFIGS[embedId as EmbedId]?.title || embedId}`);
      setPreloadedEmbeds(prev => {
        const newSet = new Set(Array.from(prev));
        newSet.add(embedId);
        return newSet;
      });
      // Keep iframe hidden but in DOM for caching
      iframe.style.display = 'none';
    };

    const handleError = () => {
      console.warn(`❌ Failed to preload: ${embedId}`);
      if (document.body.contains(iframe)) {
        document.body.removeChild(iframe);
      }
    };

    iframe.addEventListener('load', handleLoad);
    iframe.addEventListener('error', handleError);
    document.body.appendChild(iframe);
  };

  const getEmbedUrl = (embedId: string): string | null => {
    return EMBED_CONFIGS[embedId as EmbedId]?.url || null;
  };

  const isEmbedPreloaded = (embedId: string): boolean => {
    return preloadedEmbeds.has(embedId);
  };

  return (
    <EmbedCacheContext.Provider value={{ preloadedEmbeds, isEmbedPreloaded, getEmbedUrl }}>
      {children}
    </EmbedCacheContext.Provider>
  );
}

export function useEmbedCache() {
  const context = useContext(EmbedCacheContext);
  if (!context) {
    throw new Error("useEmbedCache must be used within EmbedCacheProvider");
  }
  return context;
}

// Optimized embed component that shows loading state until preloaded
interface OptimizedEmbedProps {
  embedId: string;
  className?: string;
}

export function OptimizedEmbed({ embedId, className = "h-full w-full" }: OptimizedEmbedProps) {
  const { isEmbedPreloaded, getEmbedUrl } = useEmbedCache();
  const [showEmbed, setShowEmbed] = useState(false);
  
  const embedUrl = getEmbedUrl(embedId);
  const isPreloaded = isEmbedPreloaded(embedId);

  useEffect(() => {
    if (isPreloaded) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => setShowEmbed(true), 100);
      return () => clearTimeout(timer);
    }
  }, [isPreloaded]);

  if (!embedUrl) {
    return <div className={className}>Embed not found</div>;
  }

  return (
    <div className={className}>
      {!showEmbed && (
        <div className="h-full w-full flex items-center justify-center bg-muted">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      )}
      
      {showEmbed && (
        <div
          className="h-full w-full"
          dangerouslySetInnerHTML={{
            __html: `<div style="left: 0; width: 100%; height: 100%; position: relative; overflow: hidden;"><iframe src="${embedUrl}" style="top: 0; left: 0; width: 100%; height: 100%; position: absolute; border: 0; margin: 0; padding: 0;" allowfullscreen allow="fullscreen *; autoplay; camera; microphone; geolocation;"></iframe></div>`
          }}
        />
      )}
    </div>
  );
}

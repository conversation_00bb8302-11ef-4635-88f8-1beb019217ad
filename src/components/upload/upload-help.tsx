"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  InfoIcon, 
  CheckCircleIcon, 
  AlertTriangleIcon, 
  FileTextIcon,
  DownloadIcon 
} from 'lucide-react';
import { Button } from '@/components/ui/button';

export function UploadHelp() {
  const sampleData = [
    {
      Date: "06/24/2025",
      Merchant: "The Home Depot",
      Domain: "homedepot.com",
      "Order Amount USD": "161.99",
      "Commission USD": "1.33",
      "Code Used": "-",
      Status: "active"
    },
    {
      Date: "06/24/2025",
      Merchant: "Spanx",
      Domain: "spanx.com",
      "Order Amount USD": "138.00",
      "Commission USD": "13.80",
      "Code Used": "WELCOME15",
      Status: "active"
    }
  ];

  const downloadSampleCsv = () => {
    const headers = ["Date", "Merchant", "Domain", "Order Amount USD", "Commission USD", "Code Used", "Status"];
    const csvContent = [
      headers.join(","),
      ...sampleData.map(row => 
        headers.map(header => `"${row[header as keyof typeof row]}"`).join(",")
      )
    ].join("\n");

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'shopmy-sample.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* CSV Format Requirements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileTextIcon className="h-5 w-5" />
            CSV Format Requirements
          </CardTitle>
          <CardDescription>
            Your CSV file must include these required columns in any order
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Required Columns</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600 border-green-200">Required</Badge>
                  <span className="text-sm font-mono">Date</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600 border-green-200">Required</Badge>
                  <span className="text-sm font-mono">Merchant</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600 border-green-200">Required</Badge>
                  <span className="text-sm font-mono">Order Amount USD</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600 border-green-200">Required</Badge>
                  <span className="text-sm font-mono">Status</span>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Optional Columns</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-blue-600 border-blue-200">Optional</Badge>
                  <span className="text-sm font-mono">Domain</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-blue-600 border-blue-200">Optional</Badge>
                  <span className="text-sm font-mono">Commission USD</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-blue-600 border-blue-200">Optional</Badge>
                  <span className="text-sm font-mono">Code Used</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sample Data */}
      <Card>
        <CardHeader>
          <CardTitle>Sample CSV Format</CardTitle>
          <CardDescription>
            Example of properly formatted Shopmy CSV data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="overflow-x-auto">
              <table className="w-full text-sm border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Date</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Merchant</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Domain</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Order Amount USD</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Commission USD</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Code Used</th>
                    <th className="border border-gray-200 px-3 py-2 text-left font-mono">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {sampleData.map((row, index) => (
                    <tr key={index}>
                      <td className="border border-gray-200 px-3 py-2 font-mono">{row.Date}</td>
                      <td className="border border-gray-200 px-3 py-2">{row.Merchant}</td>
                      <td className="border border-gray-200 px-3 py-2 font-mono">{row.Domain}</td>
                      <td className="border border-gray-200 px-3 py-2 font-mono">{row["Order Amount USD"]}</td>
                      <td className="border border-gray-200 px-3 py-2 font-mono">{row["Commission USD"]}</td>
                      <td className="border border-gray-200 px-3 py-2 font-mono">{row["Code Used"]}</td>
                      <td className="border border-gray-200 px-3 py-2">{row.Status}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <Button onClick={downloadSampleCsv} variant="outline" size="sm">
              <DownloadIcon className="mr-2 h-4 w-4" />
              Download Sample CSV
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Data Format Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Data Format Guidelines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-medium text-sm flex items-center gap-2">
                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  Supported Formats
                </h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Date: MM/DD/YYYY (e.g., 06/24/2025)</li>
                  <li>• Amount: Decimal numbers (e.g., 161.99)</li>
                  <li>• Status: active, pending, declined, cancelled</li>
                  <li>• Encoding: UTF-8</li>
                  <li>• File size: Maximum 10MB</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium text-sm flex items-center gap-2">
                  <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />
                  Common Issues
                </h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Missing required columns</li>
                  <li>• Invalid date formats</li>
                  <li>• Non-numeric amounts</li>
                  <li>• Special characters in merchant names</li>
                  <li>• Empty rows or cells</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Processing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Processing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                <strong>Duplicate Detection:</strong> The system automatically detects and skips duplicate transactions based on date, merchant, and amount to prevent data duplication.
              </AlertDescription>
            </Alert>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">What happens during processing:</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>1. File validation and format checking</li>
                  <li>2. Row-by-row data transformation</li>
                  <li>3. Duplicate detection and filtering</li>
                  <li>4. Database insertion and indexing</li>
                  <li>5. Analytics data refresh</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">After successful upload:</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Data appears in dashboard charts</li>
                  <li>• Transactions visible in spreadsheet view</li>
                  <li>• Analytics metrics updated</li>
                  <li>• Upload history recorded</li>
                  <li>• Error report available (if any)</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Troubleshooting */}
      <Card>
        <CardHeader>
          <CardTitle>Troubleshooting</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Common Error Solutions</h4>
              <div className="space-y-3">
                <div className="border-l-4 border-red-200 pl-4">
                  <p className="font-medium text-sm">Error: "Missing required column"</p>
                  <p className="text-sm text-muted-foreground">
                    Ensure your CSV has columns named exactly: Date, Merchant, Order Amount USD, and Status
                  </p>
                </div>
                <div className="border-l-4 border-yellow-200 pl-4">
                  <p className="font-medium text-sm">Error: "Invalid date format"</p>
                  <p className="text-sm text-muted-foreground">
                    Use MM/DD/YYYY format (e.g., 06/24/2025). Avoid text dates or different formats.
                  </p>
                </div>
                <div className="border-l-4 border-blue-200 pl-4">
                  <p className="font-medium text-sm">Error: "Invalid order amount"</p>
                  <p className="text-sm text-muted-foreground">
                    Remove currency symbols ($) and ensure amounts are numeric (e.g., 161.99 not $161.99)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import * as React from "react";
import { CalendarIcon, FilterIcon, XIcon } from "lucide-react";
import { dateToLocalDateString, formatDateForDisplay, safeStringToDate } from "@/lib/date-utils";



import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useFilterOptions } from "@/hooks/useFilterOptions";
import { useIsMobile } from "@/hooks/use-mobile";

interface TransactionFiltersProps {
  onFiltersChange: (filters: {
    platform?: string;
    merchant?: string;
    status?: string;
    network?: string;
    transactionType?: string;
    startDate?: string;
    endDate?: string;
  }) => void;
  initialFilters?: {
    platform?: string;
    merchant?: string;
    status?: string;
    network?: string;
    transactionType?: string;
    startDate?: string;
    endDate?: string;
  };
}

export function TransactionFilters({ onFiltersChange, initialFilters = {} }: TransactionFiltersProps) {
  const isMobile = useIsMobile();
  // Fetch dynamic filter options
  const { data: filterOptions, loading: filterOptionsLoading, error: filterOptionsError } = useFilterOptions();
  const [filters, setFilters] = React.useState(initialFilters);
  const [startDate, setStartDate] = React.useState<Date | undefined>(
    initialFilters.startDate ? safeStringToDate(initialFilters.startDate) : undefined
  );
  const [endDate, setEndDate] = React.useState<Date | undefined>(
    initialFilters.endDate ? safeStringToDate(initialFilters.endDate) : undefined
  );

  // Update filters when they change
  React.useEffect(() => {
    const updatedFilters = {
      ...filters,
      // Use dates as-is without timezone adjustments
      startDate: startDate ? dateToLocalDateString(startDate) : undefined,
      endDate: endDate ? dateToLocalDateString(endDate) : undefined,
    };
    onFiltersChange(updatedFilters);
  }, [filters.platform, filters.merchant, filters.status, filters.network, filters.transactionType, startDate, endDate]); // Remove onFiltersChange from deps

  const handleFilterChange = (key: string, value: string | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setStartDate(undefined);
    setEndDate(undefined);
  };

  const activeFiltersCount = Object.values({
    ...filters,
    startDate,
    endDate
  }).filter(Boolean).length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FilterIcon className="h-5 w-5" />
              Filters
            </CardTitle>
            <CardDescription>
              Filter transactions by platform, merchant, network, status, transaction type, and date range
              {filterOptionsError && (
                <span className="text-red-600 block mt-1">
                  Error loading filter options: {filterOptionsError}
                </span>
              )}
            </CardDescription>
          </div>
          {activeFiltersCount > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} active
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
              >
                <XIcon className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-7'}`}>
          {/* Platform Filter */}
          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Select
              value={filters.platform || undefined}
              onValueChange={(value) => handleFilterChange("platform", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All platforms"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All platforms</SelectItem>
                {filterOptions?.platforms.map((platform) => (
                  <SelectItem key={platform.value} value={platform.value}>
                    {platform.label} ({platform.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Merchant Filter */}
          <div className="space-y-2">
            <Label htmlFor="merchant">Merchant</Label>
            <Select
              value={filters.merchant || undefined}
              onValueChange={(value) => handleFilterChange("merchant", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All merchants"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All merchants</SelectItem>
                {filterOptions?.merchants.map((merchant) => (
                  <SelectItem key={merchant.value} value={merchant.value}>
                    {merchant.label} ({merchant.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={filters.status || undefined}
              onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All statuses"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                {filterOptions?.statuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label} ({status.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Transaction Type Filter */}
          <div className="space-y-2">
            <Label htmlFor="transactionType">Type</Label>
            <Select
              value={filters.transactionType || undefined}
              onValueChange={(value) => handleFilterChange("transactionType", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All types"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                {filterOptions?.transactionTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label} ({type.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Network Filter */}
          <div className="space-y-2">
            <Label htmlFor="network">Network</Label>
            <Select
              value={filters.network || undefined}
              onValueChange={(value) => handleFilterChange("network", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All networks"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All networks</SelectItem>
                {filterOptions?.networks.map((network) => (
                  <SelectItem key={network.value} value={network.value}>
                    {network.label} ({network.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Start Date Filter */}
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? formatDateForDisplay(startDate) : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  autoFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date Filter */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? formatDateForDisplay(endDate) : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  autoFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

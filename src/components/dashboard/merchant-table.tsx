import React from "react";
import {
  ColumnDef,
  SortingState,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnFiltersState,
  PaginationState,
} from "@tanstack/react-table";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Trash2,
  ChevronsUpDown,
  ChevronUp,
  ChevronDown,
  Edit,
  ChevronsLeft,
  ChevronLeft,
  ChevronRight,
  ChevronsRight,
} from "lucide-react";
import type { MerchantOverride } from "@/types/linkbuilder";
import { useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

const PAGE_SIZE_OPTIONS = [5, 10, 20, 50, 100];

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const getSortIcon = (isSorted: false | "asc" | "desc") => {
  if (isSorted === "asc") return <ChevronUp className="ml-1 h-4 w-4 inline" />;
  if (isSorted === "desc")
    return <ChevronDown className="ml-1 h-4 w-4 inline" />;
  return <ChevronsUpDown className="ml-1 h-4 w-4 opacity-40 inline" />;
};

interface MerchantTableProps {
  data: MerchantOverride[];
  onDelete: (domain: string) => void;
  onEdit: (
    domain: string,
    updates: Partial<
      Pick<MerchantOverride, "preferredNetworkId" | "isBlacklisted">
    >
  ) => void;
}

const MerchantTable: React.FC<MerchantTableProps> = ({
  data,
  onDelete,
  onEdit,
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const [confirmDomain, setConfirmDomain] = useState<string | null>(null);
  const [editingOverride, setEditingOverride] =
    useState<MerchantOverride | null>(null);
  const [editForm, setEditForm] = useState<{
    preferredNetworkId: string;
    isBlacklisted: boolean;
  }>({ preferredNetworkId: "", isBlacklisted: false });
  const [validationError, setValidationError] = useState<string>("");
  const allNetworks = React.useMemo(() => {
    const set = new Set<string>();
    data.forEach((row: MerchantOverride) => {
      if (row.preferredNetworkId) set.add(row.preferredNetworkId);
    });
    return Array.from(set).sort();
  }, [data]);
  const networkFilter =
    (columnFilters.find((f) => f.id === "preferredNetworkId")
      ?.value as string[]) || [];
  const setNetworkFilter = (value: string[]) => {
    setColumnFilters((old) => [
      ...old.filter((f) => f.id !== "preferredNetworkId"),
      ...(value.length ? [{ id: "preferredNetworkId", value }] : []),
    ]);
  };

  const columns = React.useMemo<ColumnDef<MerchantOverride, any>[]>(
    () => [
      {
        accessorKey: "domain",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="p-0 h-auto text-left font-semibold text-foreground hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Domain {getSortIcon(column.getIsSorted())}
          </Button>
        ),
        cell: (info) => info.getValue(),
        enableSorting: true,
        enableColumnFilter: true,
      },
      {
        accessorKey: "preferredNetworkId",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="p-0 h-auto text-left font-semibold text-foreground hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Preferred Network {getSortIcon(column.getIsSorted())}
          </Button>
        ),
        cell: (info) => info.getValue() || "-",
        enableSorting: true,
        enableColumnFilter: true,
        filterFn: (row, columnId, filterValue) => {
          if (!Array.isArray(filterValue) || filterValue.length === 0)
            return true;
          return filterValue.includes(row.getValue(columnId));
        },
      },
      {
        accessorKey: "isBlacklisted",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="p-0 h-auto text-left font-semibold text-foreground hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Blacklisted {getSortIcon(column.getIsSorted())}
          </Button>
        ),
        cell: (info) =>
          info.getValue() ? (
            <Badge variant="destructive">Yes</Badge>
          ) : (
            <Badge variant="secondary">No</Badge>
          ),
        enableSorting: true,
        enableColumnFilter: true,
        filterFn: (row, columnId, filterValue) => {
          if (filterValue === "all") return true;
          if (filterValue === "yes") return row.getValue(columnId) === true;
          if (filterValue === "no") return row.getValue(columnId) === false;
          return true;
        },
      },
      {
        accessorKey: "updatedAt",
        header: ({ column }) => (
          <Button
            variant="ghost"
            className="p-0 h-auto text-left font-semibold text-foreground hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Updated {getSortIcon(column.getIsSorted())}
          </Button>
        ),
        cell: (info) => formatDate(info.getValue()),
        enableSorting: true,
      },
      {
        id: "actions",
        header: () => <div className="text-center w-full">Actions</div>,
        cell: ({ row }) => {
          const override = row.original;
          return (
            <div className="flex justify-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setEditingOverride(override);
                  setEditForm({
                    preferredNetworkId: override.preferredNetworkId || "",
                    isBlacklisted: override.isBlacklisted,
                  });
                  setValidationError("");
                }}
                aria-label={`Edit override for ${override.domain}`}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfirmDomain(override.domain)}
                aria-label={`Delete override for ${override.domain}`}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          );
        },
        enableSorting: false,
        enableColumnFilter: false,
      },
    ],
    [setEditingOverride, setEditForm, setValidationError, setConfirmDomain]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    getRowId: (row) => row.domain,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const domainFilter =
    (columnFilters.find((f) => f.id === "domain")?.value as string) || "";
  const blacklistedFilter =
    (columnFilters.find((f) => f.id === "isBlacklisted")?.value as string) ||
    "all";

  const setDomainFilter = (value: string) => {
    setColumnFilters((old) => [
      ...old.filter((f) => f.id !== "domain"),
      ...(value ? [{ id: "domain", value }] : []),
    ]);
  };

  const setBlacklistedFilter = (value: string) => {
    setColumnFilters((old) => [
      ...old.filter((f) => f.id !== "isBlacklisted"),
      ...(value !== "all" ? [{ id: "isBlacklisted", value }] : []),
    ]);
  };

  return (
    <div className="space-y-4">
      {/* Filters Section */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
        <div className="space-y-2">
          <label className="text-sm font-medium">Domain</label>
          <Input
            placeholder="Filter domains..."
            value={domainFilter}
            onChange={(e) => setDomainFilter(e.target.value)}
            className="h-9"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Selected Networks</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-9 w-full truncate"
                title={
                  networkFilter.length === 0 ? "All" : networkFilter.join(", ")
                }
              >
                <span className="truncate w-full text-left">
                  {networkFilter.length === 0
                    ? "All"
                    : networkFilter.join(", ")}
                </span>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" className="min-w-fit p-1">
              <div className="flex flex-col gap-1 max-h-48 overflow-y-auto py-2">
                {allNetworks.map((network) => (
                  <button
                    key={network}
                    className={`flex items-center px-2 py-1 rounded hover:bg-muted/60 text-left ${networkFilter.includes(network) ? "bg-muted/40" : ""}`}
                    onClick={() => {
                      if (networkFilter.includes(network)) {
                        setNetworkFilter(
                          networkFilter.filter((n) => n !== network).sort()
                        );
                      } else {
                        setNetworkFilter([...networkFilter, network].sort());
                      }
                    }}
                    type="button"
                  >
                    <Check
                      className={`mr-2 h-4 w-4 ${networkFilter.includes(network) ? "opacity-100" : "opacity-0"}`}
                    />
                    {network}
                  </button>
                ))}
              </div>
              {allNetworks.length === 0 && (
                <div className="text-xs text-muted-foreground">No networks</div>
              )}
              <div className="mt-2 flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  className="flex-1"
                  onClick={() => setNetworkFilter([])}
                >
                  Clear
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => setNetworkFilter(allNetworks)}
                >
                  All
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Blacklisted</label>
          <Select
            value={blacklistedFilter}
            onValueChange={setBlacklistedFilter}
          >
            <SelectTrigger className="h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="yes">Yes</SelectItem>
              <SelectItem value="no">No</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Rows per page</label>
          <Select
            value={String(pagination.pageSize)}
            onValueChange={(v) =>
              setPagination((p) => ({
                ...p,
                pageSize: Number(v),
                pageIndex: 0,
              }))
            }
          >
            <SelectTrigger className="h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {PAGE_SIZE_OPTIONS.map((opt) => (
                <SelectItem key={opt} value={String(opt)}>
                  {opt}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Table Section */}
      <div className="overflow-hidden rounded-lg border bg-background">
        <Table className="w-full text-[15px]">
          <TableHeader className="bg-muted">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="h-13">
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    className={
                      header.column.id === "domain"
                        ? "font-semibold px-4 py-3 text-left w-[180px]"
                        : header.column.id === "preferredNetworkId"
                          ? "font-semibold px-4 py-3 text-left w-[180px]"
                          : header.column.id === "isBlacklisted"
                            ? "font-semibold px-4 py-3 text-center w-[110px]"
                            : header.column.id === "updatedAt"
                              ? "font-semibold px-4 py-3 text-right w-[170px] min-w-[120px] max-w-[200px]"
                              : header.column.id === "actions"
                                ? "font-semibold px-3 py-3 text-center w-[70px]"
                                : "font-semibold px-4 py-3"
                    }
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="h-13 hover:bg-muted/60 transition-colors group"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={
                        cell.column.id === "domain"
                          ? "px-4 py-3 text-left truncate"
                          : cell.column.id === "preferredNetworkId"
                            ? "px-4 py-3 text-left truncate"
                            : cell.column.id === "isBlacklisted"
                              ? "px-4 py-3 text-center"
                              : cell.column.id === "updatedAt"
                                ? "px-4 py-3 text-right whitespace-nowrap"
                                : cell.column.id === "actions"
                                  ? "px-3 py-3 text-center"
                                  : "px-4 py-3"
                      }
                      style={{ verticalAlign: "middle" }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-16 text-center text-muted-foreground text-sm"
                >
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-4 py-2 border-t bg-muted/25 text-xs">
          <div className="flex items-center space-x-2">
            <span className="text-muted-foreground">
              Showing{" "}
              {table.getState().pagination.pageIndex *
                table.getState().pagination.pageSize +
                1}{" "}
              to{" "}
              {Math.min(
                (table.getState().pagination.pageIndex + 1) *
                  table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}{" "}
              of {table.getFilteredRowModel().rows.length} results
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <span className="text-muted-foreground">Page</span>
              <span className="font-medium">
                {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                className="px-2 py-1 h-7"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
                aria-label="First page"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="px-2 py-1 h-7"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
                aria-label="Previous page"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="px-2 py-1 h-7"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
                aria-label="Next page"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="px-2 py-1 h-7"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
                aria-label="Last page"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      {/* Edit Dialog */}
      {editingOverride && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 w-full max-w-sm">
            <div className="mb-4">
              <h2 className="text-lg font-semibold mb-2">Edit Override</h2>
              <p className="mb-4">
                Editing override for{" "}
                <span className="font-bold"> {editingOverride.domain} </span>
              </p>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-6">
                <p className="text-xs text-yellow-800 dark:text-yellow-200">
                  <strong>Note:</strong> Avoid editing if you don't know the
                  exact network ID. Incorrect values may affect routing
                  behavior.
                </p>
              </div>

              <div className="space-y-4 mb-4">
                <div className="space-y-2">
                  <Label htmlFor="preferredNetwork">Preferred Network</Label>
                  <Input
                    id="preferredNetwork"
                    placeholder="Enter network ID..."
                    value={editForm.preferredNetworkId}
                    onChange={(e) => {
                      const value = e.target.value;
                      setEditForm((prev) => ({
                        ...prev,
                        preferredNetworkId: value,
                      }));

                      // Basic validation - network ID should be alphanumeric with possible dashes/underscores
                      if (value && !/^[a-zA-Z0-9_-]+$/.test(value)) {
                        setValidationError(
                          "Network ID should only contain letters, numbers, dashes, and underscores"
                        );
                      } else {
                        setValidationError("");
                      }
                    }}
                    className={validationError ? "border-red-500" : ""}
                  />
                  {validationError && (
                    <p className="text-xs text-red-500 mt-1">
                      {validationError}
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isBlacklisted"
                    checked={editForm.isBlacklisted}
                    onCheckedChange={(checked) =>
                      setEditForm((prev) => ({
                        ...prev,
                        isBlacklisted: checked,
                      }))
                    }
                  />
                  <Label htmlFor="isBlacklisted">Blacklisted</Label>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setEditingOverride(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (validationError) return;

                  onEdit(editingOverride.domain, {
                    preferredNetworkId: editForm.preferredNetworkId || null,
                    isBlacklisted: editForm.isBlacklisted,
                  });
                  setEditingOverride(null);
                }}
                disabled={!!validationError}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Confirm Delete Dialog */}
      {confirmDomain && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 w-full max-w-sm">
            <div className="mb-4">
              <h2 className="text-lg font-semibold mb-2">Confirm Delete</h2>
              <p>
                Are you sure you want to delete the override for{" "}
                <span className="font-bold"> {confirmDomain} </span>?
              </p>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setConfirmDomain(null)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  onDelete(confirmDomain);
                  setConfirmDomain(null);
                }}
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MerchantTable;

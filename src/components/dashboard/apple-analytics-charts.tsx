"use client";

import * as React from "react";
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AppleAnalyticsData } from '@/hooks/useAppleAnalytics';
import { formatLargeNumber } from '@/lib/analytics-utils';

interface AppleAnalyticsChartsProps {
  analyticsData?: AppleAnalyticsData;
  loading?: boolean;
  error?: string;
}

export function AppleAnalyticsCharts({
  analyticsData,
  loading,
  error
}: AppleAnalyticsChartsProps) {

  // Prepare DAU chart data
  const dauChartData = React.useMemo(() => {
    if (!analyticsData?.dailySummary) return [];

    return analyticsData.dailySummary.map(day => ({
      date: day.report_date,
      dau: day.daily_active_users,
      sessions: day.daily_sessions,
      timeSpentHours: parseFloat(day.daily_duration_hours),
      avgSessionDuration: parseFloat(day.avg_session_duration_minutes),
      sessionsPerUser: day.sessions_per_active_user,
      installs: day.daily_installs,
      uninstalls: day.daily_uninstalls
    }));
  }, [analyticsData]);

  // Prepare version performance data
  const versionPerformanceData = React.useMemo(() => {
    if (!analyticsData?.sessionAnalytics) return [];

    // Aggregate by app version
    const versionMap = new Map();

    analyticsData.sessionAnalytics.forEach(session => {
      const version = session.app_version;
      if (!versionMap.has(version)) {
        versionMap.set(version, {
          version,
          totalUsers: 0,
          totalSessions: 0,
          totalDuration: 0,
          avgSessionDuration: 0,
          sessionsPerUser: 0
        });
      }

      const existing = versionMap.get(version);
      existing.totalUsers += session.total_active_users;
      existing.totalSessions += session.total_sessions;
      existing.totalDuration += session.total_duration_seconds;
    });

    // Calculate averages and sort by user count
    return Array.from(versionMap.values())
      .map(version => ({
        ...version,
        avgSessionDuration: version.totalDuration / version.totalSessions,
        sessionsPerUser: version.totalSessions / version.totalUsers
      }))
      .sort((a, b) => b.totalUsers - a.totalUsers)
      .slice(0, 10); // Top 10 versions
  }, [analyticsData]);

  // Calculate total metrics for summary cards
  const totalMetrics = React.useMemo(() => {
    if (!analyticsData?.dailySummary) return null;

    const totalTimeHours = analyticsData.dailySummary.reduce((sum, day) =>
      sum + parseFloat(day.daily_duration_hours), 0);
    const totalSessions = analyticsData.dailySummary.reduce((sum, day) =>
      sum + day.daily_sessions, 0);
    const totalActiveUsers = analyticsData.dailySummary.reduce((sum, day) =>
      sum + day.daily_active_users, 0);
    const avgSessionDuration = analyticsData.dailySummary.reduce((sum, day) =>
      sum + parseFloat(day.avg_session_duration_minutes), 0) / analyticsData.dailySummary.length;

    // Calculate average time per user in minutes
    const avgTimePerUserHours = totalTimeHours / (totalActiveUsers / analyticsData.dailySummary.length);
    const avgTimePerUserMinutes = avgTimePerUserHours * 60;
    const minutes = Math.floor(avgTimePerUserMinutes);
    const seconds = Math.floor((avgTimePerUserMinutes - minutes) * 60);

    return {
      totalTimeHours: totalTimeHours.toFixed(1),
      avgTimePerUser: `${minutes}m ${seconds}s`,
      totalSessions,
      avgSessionDuration: avgSessionDuration.toFixed(1)
    };
  }, [analyticsData]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <CardTitle>Loading...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center">
                  <div className="text-sm text-muted-foreground">Loading chart...</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-sm text-destructive">Error loading charts: {error}</div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="space-y-6">
        <div className="text-sm text-muted-foreground">No chart data available</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Spent Analytics Cards */}
      {totalMetrics && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Time Spent Analytics</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Time Spent
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalMetrics.totalTimeHours}h</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Across all users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Avg Time per User
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalMetrics.avgTimePerUser}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Per day average
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Sessions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatLargeNumber(totalMetrics.totalSessions)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  All time period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Avg Session Duration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalMetrics.avgSessionDuration}m</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Per session
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* DAU Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Daily Active Users</h3>
        <Card>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={dauChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => {
                    const date = new Date(value + 'T12:00:00');
                    return date.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  className="text-muted-foreground"
                />
                <YAxis className="text-muted-foreground" />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-card border border-border rounded-lg p-3 shadow-lg">
                          <p className="font-medium text-card-foreground mb-2">
                            {new Date(label + 'T12:00:00').toLocaleDateString("en-US", {
                              weekday: "long",
                              month: "short",
                              day: "numeric",
                            })}
                          </p>
                          <div className="space-y-1 text-sm text-card-foreground">
                            <p><span className="font-medium">Daily Active Users:</span> {formatLargeNumber(data.dau)}</p>
                            <p><span className="font-medium">Total Sessions:</span> {formatLargeNumber(data.sessions)}</p>
                            <p><span className="font-medium">Time Spent:</span> {data.timeSpentHours.toFixed(1)} hours</p>
                            <p><span className="font-medium">Avg Session:</span> {data.avgSessionDuration.toFixed(1)} minutes</p>
                            <p><span className="font-medium">Sessions/User:</span> {data.sessionsPerUser.toFixed(1)}</p>
                            <p><span className="font-medium">New Installs:</span> {formatLargeNumber(data.installs)}</p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="dau"
                  fill="#3b82f6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Version Performance Chart */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">App Version Performance</h3>
        <Card>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={versionPerformanceData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="version"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  className="text-muted-foreground"
                />
                <YAxis className="text-muted-foreground" />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-card border border-border rounded-lg p-3 shadow-lg">
                          <p className="font-medium text-card-foreground mb-2">Version {label}</p>
                          <div className="space-y-1 text-sm text-card-foreground">
                            <p><span className="font-medium">Active Users:</span> {formatLargeNumber(data.totalUsers)}</p>
                            <p><span className="font-medium">Total Sessions:</span> {formatLargeNumber(data.totalSessions)}</p>
                            <p><span className="font-medium">Avg Session Duration:</span> {(data.avgSessionDuration / 60).toFixed(1)} minutes</p>
                            <p><span className="font-medium">Sessions per User:</span> {data.sessionsPerUser.toFixed(1)}</p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="totalUsers"
                  fill="#10b981"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

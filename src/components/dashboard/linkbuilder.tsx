"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, ExternalLink, Trash2, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  MerchantData,
  MerchantOverride,
  MerchantUrl,
} from "@/types/linkbuilder";
import { validateUrl, extractDomainFromUrl } from "@/utils/utils";
import MerchantTable from "@/components/dashboard/merchant-table";
import { Switch } from "@/components/ui/switch";
import { X } from "lucide-react";

export function Linkbuilder() {
  const [url, setUrl] = useState("");
  const [device, setDevice] = useState("MOBILE");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [merchantUrlData, setMerchantUrlData] = useState<MerchantUrl | null>(
    null
  );
  const [availableMerchants, setAvailableMerchants] = useState<MerchantData[]>(
    []
  );
  const [selectedNetworkId, setSelectedNetworkId] = useState("");
  const [isBlacklisted, setIsBlacklisted] = useState(false);
  const [isCreatingOverride, setIsCreatingOverride] = useState(false);
  const [overrides, setOverrides] = useState<MerchantOverride[]>([]);
  const [isLoadingOverrides, setIsLoadingOverrides] = useState(true);
  const [expandedMerchant, setExpandedMerchant] = useState<string | null>(null);
  const { toast } = useToast();
  const [isRefreshingOverrides, setIsRefreshingOverrides] = useState(false);
  const [isMerchantResultObsolete, setIsMerchantResultObsolete] =
    useState(false);
  const [overridesDirty, setOverridesDirty] = useState(false);
  const [persistentToastId, setPersistentToastId] = useState<string | null>(
    null
  );

  const showPersistentRefreshToast = () => {
    if (persistentToastId) {
      setOverridesDirty(true);
      return;
    }
    setOverridesDirty(true);
  };

  const dismissPersistentToast = () => {
    setOverridesDirty(false);
    setPersistentToastId(null);
  };

  useEffect(() => {
    fetchOverrides();
  }, []);

  const fetchOverrides = async (clearDirty = false) => {
    try {
      setIsLoadingOverrides(true);
      setIsRefreshingOverrides(true);
      const response = await fetch("/api/linkbuilder/overrides");
      const result = await response.json();
      if (result.success && result.data) {
        const sortedOverrides = result.data.sort(
          (a: MerchantOverride, b: MerchantOverride) => {
            return (
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
          }
        );
        setOverrides(sortedOverrides);
        if (clearDirty) setOverridesDirty(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load merchant overrides",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching merchant overrides:", error);
      toast({
        title: "Error",
        description: "Failed to load merchant overrides",
        variant: "destructive",
      });
    } finally {
      setIsLoadingOverrides(false);
      setIsRefreshingOverrides(false);
    }
  };

  const createMerchantUrl = async () => {
    const validation = validateUrl(url);

    if (!validation.isValid) {
      toast({
        title: "Invalid URL",
        description: validation.error || "Please enter a valid URL format",
        variant: "destructive",
      });
      return;
    }

    if (validation.error && url.startsWith("http://")) {
      toast({
        title: "Security Warning",
        description: validation.error,
        variant: "default",
      });
    }

    try {
      setIsAnalyzing(true);
      const response = await fetch("/api/linkbuilder/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url: url.trim(), device }),
      });
      const result = await response.json();
      if (result.success && result.data) {
        setMerchantUrlData(result.data.merchantUrl);
        setAvailableMerchants(result.data.availableMerchants);
        if (result.data.availableMerchants.length > 0) {
          setSelectedNetworkId(result.data.availableMerchants[0].networkId);
        }
        toast({
          title: "Success",
          description: "Merchant URL created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to create merchant URL",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating merchant URL:", error);
      toast({
        title: "Error",
        description: "Failed to create merchant URL",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const createOverride = async () => {
    if (!selectedNetworkId) {
      toast({
        title: "Error",
        description: "Please select a preferred network",
        variant: "destructive",
      });
      return;
    }
    try {
      setIsCreatingOverride(true);
      const response = await fetch("/api/linkbuilder/overrides", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          domain: extractDomainFromUrl(url),
          isBlacklisted,
          preferredNetworkId: selectedNetworkId,
        }),
      });
      const result = await response.json();
      if (result.success) {
        toast({
          title: "Success",
          description:
            result.message || "Merchant override created successfully",
        });
        setIsMerchantResultObsolete(true);
        setIsBlacklisted(false);
        if (availableMerchants.length > 0) {
          setSelectedNetworkId(availableMerchants[0].networkId);
        } else {
          setSelectedNetworkId("");
        }
      } else {
        throw new Error(result.error || "Failed to create merchant override");
      }
    } catch (error) {
      console.error("Error creating merchant override:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to create merchant override",
        variant: "destructive",
      });
    } finally {
      setIsCreatingOverride(false);
    }
  };

  const deleteOverride = async (domain: string) => {
    try {
      const response = await fetch(`/api/linkbuilder/overrides/${domain}`, {
        method: "DELETE",
      });
      const result = await response.json();
      if (result.success) {
        toast({
          title: "Success",
          description:
            result.message || "Merchant override deleted successfully",
        });
        setIsMerchantResultObsolete(true);
        showPersistentRefreshToast();
      } else {
        throw new Error(result.error || "Failed to delete merchant override");
      }
    } catch (error) {
      console.error("Error deleting merchant override:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete merchant override",
        variant: "destructive",
      });
    }
  };

  const handleEditOverride = async (
    domain: string,
    updates: Partial<
      Pick<MerchantOverride, "preferredNetworkId" | "isBlacklisted">
    >
  ) => {
    try {
      const normalizedUpdates = {
        ...updates,
        preferredNetworkId:
          updates.preferredNetworkId === "" ? null : updates.preferredNetworkId,
      };

      const response = await fetch("/api/linkbuilder/overrides", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ domain, ...normalizedUpdates }),
      });
      const result = await response.json();
      if (result.success) {
        toast({
          title: "Success",
          description:
            result.message || "Merchant override updated successfully",
        });
        setIsMerchantResultObsolete(true);
        showPersistentRefreshToast();
      } else {
        throw new Error(result.error || "Failed to update merchant override");
      }
    } catch (error) {
      console.error("Error updating merchant override:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update merchant override",
        variant: "destructive",
      });
    }
  };

  const formatCommissionRate = (rate: string) => {
    const numRate = parseFloat(rate);
    return `${numRate}%`;
  };

  const handleCopy = () => {
    if (merchantUrlData?.url) {
      navigator.clipboard.writeText(merchantUrlData.url);
      toast({
        title: "Copied",
        description: "Merchant URL copied to clipboard",
      });
    }
  };

  const handleMerchantResultRefresh = async () => {
    try {
      setIsRefreshingOverrides(true);
      await Promise.all([createMerchantUrl(), fetchOverrides()]);
      setIsMerchantResultObsolete(false);
    } catch (error) {
      console.error("Error refreshing merchant data:", error);
      toast({
        title: "Error",
        description: "Failed to refresh merchant data",
        variant: "destructive",
      });
    } finally {
      setIsRefreshingOverrides(false);
    }
  };

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Create Merchant URL</CardTitle>
          <CardDescription>
            Enter a website URL to generate an affiliate merchant URL and view
            eligible merchants
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Label htmlFor="url">Website URL</Label>
              <Input
                id="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && createMerchantUrl()}
              />
            </div>
            <div className="w-32">
              <Label htmlFor="device">Device</Label>
              <Select value={device} onValueChange={setDevice}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="MOBILE">Mobile</SelectItem>
                  <SelectItem value="DESKTOP">Desktop</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={createMerchantUrl} disabled={isAnalyzing}>
                {isAnalyzing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  "Create URL"
                )}
              </Button>
            </div>
          </div>

          <div className="relative">
            {(merchantUrlData || availableMerchants.length > 0) &&
              isMerchantResultObsolete && (
                <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-black/40 backdrop-blur-sm rounded-lg">
                  <div className="mb-2 text-yellow-100 font-medium text-sm">
                    The merchant URL and eligible merchants may be outdated due
                    to recent override changes.
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleMerchantResultRefresh}
                    disabled={isRefreshingOverrides}
                    className="border-yellow-200 text-yellow-100 hover:bg-yellow-900/20"
                  >
                    {isRefreshingOverrides ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Refreshing...
                      </>
                    ) : (
                      "Refresh"
                    )}
                  </Button>
                </div>
              )}
            <div
              className={
                isMerchantResultObsolete
                  ? "pointer-events-none filter blur-sm select-none"
                  : ""
              }
            >
              {(merchantUrlData || availableMerchants.length > 0) && (
                <Card className="mb-0">
                  <CardContent className="space-y-0 p-2">
                    {merchantUrlData && (
                      <div className="flex flex-col gap-1 p-2">
                        <Label className="text-sm font-medium">
                          Generated URL
                        </Label>
                        <div className="flex items-center space-x-2 mt-1">
                          <code className="bg-background px-2 py-1 rounded text-sm flex-1 truncate">
                            {merchantUrlData.url}
                          </code>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleCopy}
                            aria-label="Copy merchant URL to clipboard"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              window.open(merchantUrlData.url, "_blank")
                            }
                            aria-label="Open merchant URL in new tab"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                    {merchantUrlData && availableMerchants.length > 0 && (
                      <div className="py-1 border-t border-muted" />
                    )}
                    {availableMerchants.length > 0 && (
                      <div className="space-y-2 p-2">
                        <Label className="text-sm font-medium mb-2 block">
                          Available Merchants
                        </Label>
                        {availableMerchants.map((merchant) => {
                          const isUsedMerchant =
                            merchantUrlData?.merchantId ===
                            merchant.advertiserId;
                          return (
                            <Card
                              key={merchant.advertiserId}
                              className={`overflow-hidden border ${
                                isUsedMerchant
                                  ? "bg-green-800 shadow-md"
                                  : "border-muted"
                              }`}
                            >
                              <button
                                type="button"
                                className={`w-full flex justify-between items-center px-4 py-3 text-left hover:bg-muted focus:outline-none ${
                                  isUsedMerchant
                                    ? "bg-green-800 hover:bg-green-700"
                                    : "bg-muted/60"
                                }`}
                                onClick={() =>
                                  setExpandedMerchant(
                                    expandedMerchant === merchant.advertiserId
                                      ? null
                                      : merchant.advertiserId
                                  )
                                }
                              >
                                <div className="flex items-center gap-2">
                                  <span className="font-medium text-base">
                                    {merchant.networkName}
                                  </span>
                                  <Badge variant="secondary">
                                    {formatCommissionRate(
                                      merchant.commissionRateMax
                                    )}
                                  </Badge>
                                  {isUsedMerchant && (
                                    <Badge
                                      variant="default"
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      Used
                                    </Badge>
                                  )}
                                  <span className="text-xs text-muted-foreground">
                                    {merchant.device}
                                  </span>
                                </div>
                                <span className="text-xs text-muted-foreground">
                                  {expandedMerchant === merchant.advertiserId
                                    ? "▲"
                                    : "▼"}
                                </span>
                              </button>
                              {expandedMerchant === merchant.advertiserId && (
                                <CardContent className="pt-2 pb-4 px-4 space-y-1 text-sm bg-background">
                                  <div>
                                    <span className="font-medium">Name:</span>{" "}
                                    {merchant.name}
                                  </div>
                                  <div>
                                    <span className="font-medium">
                                      Website:
                                    </span>{" "}
                                    {merchant.website}
                                  </div>
                                  <div>
                                    <span className="font-medium">
                                      Cookie Duration:
                                    </span>{" "}
                                    {merchant.cookieDurationHours || "-"} hours
                                  </div>
                                  <div>
                                    <span className="font-medium">
                                      Evergreen URL:
                                    </span>{" "}
                                    <span className="break-all">
                                      {merchant.evergreenUrl}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="font-medium">
                                      Network Rank:
                                    </span>{" "}
                                    {merchant.networkRank || "-"}
                                  </div>
                                  <div>
                                    <span className="font-medium">
                                      AdvertiserId:
                                    </span>{" "}
                                    {merchant.advertiserId}
                                  </div>
                                  <div>
                                    <span className="font-medium">Domain:</span>{" "}
                                    {merchant.domain}
                                  </div>
                                </CardContent>
                              )}
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {availableMerchants.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-base">
                  Set Merchant Override
                </CardTitle>
                <CardDescription>
                  Choose your preferred merchant network for this domain, or
                  blacklist it.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row md:items-end gap-4">
                  <div className="flex-1">
                    <Label className="mb-1 block">Preferred Network</Label>
                    <Select
                      value={selectedNetworkId}
                      onValueChange={setSelectedNetworkId}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select preferred network" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableMerchants.map((merchant) => (
                          <SelectItem
                            key={merchant.networkId}
                            value={merchant.networkId}
                          >
                            {merchant.name} ({merchant.networkName}) -{" "}
                            {formatCommissionRate(merchant.commissionRateMax)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <div className="mt-4">
                      <Label htmlFor="blacklist" className="text-xs mb-1 block">
                        Blacklist
                      </Label>
                      <Switch
                        id="blacklist"
                        checked={isBlacklisted}
                        onCheckedChange={setIsBlacklisted}
                      />
                    </div>
                  </div>
                  <Button
                    onClick={createOverride}
                    disabled={isCreatingOverride || !selectedNetworkId}
                    className="md:ml-4"
                  >
                    {isCreatingOverride ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Override"
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Merchant Overrides</CardTitle>
            <CardDescription>
              Manage your preferred merchant selections for different domains
            </CardDescription>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => fetchOverrides(true)}
            disabled={isRefreshingOverrides}
          >
            {isRefreshingOverrides ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              "Refresh"
            )}
          </Button>
        </CardHeader>
        {overridesDirty && (
          <div className="mb-4 mx-4">
            <div className="p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/20 border-l-4 border-yellow-400 dark:border-yellow-500 rounded-r-lg shadow-sm">
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-yellow-900 dark:text-yellow-100 font-medium text-sm">
                      Table data is outdated
                    </h4>
                    <p className="text-yellow-800 dark:text-yellow-200 text-xs mt-0.5">
                      Merchant overrides have been modified. Refresh to see
                      latest changes.
                    </p>
                  </div>
                </div>
                <div className="flex gap-2 flex-shrink-0">
                  <Button
                    size="sm"
                    onClick={() => fetchOverrides(true)}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white border-0 text-xs px-3 py-1 h-7"
                  >
                    Refresh
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={dismissPersistentToast}
                    aria-label="Dismiss notification"
                    className="text-yellow-700 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-800 w-7 h-7 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
        <CardContent>
          {isLoadingOverrides ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading overrides...</span>
            </div>
          ) : (
            <MerchantTable
              data={overrides}
              onDelete={deleteOverride}
              onEdit={handleEditOverride}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { PlatformAnalytics } from '@/lib/analytics-utils';
import { formatCurrency } from '@/lib/analytics-utils';

// Helper function to calculate metrics for specific time periods
function calculatePeriodMetrics(dataPoints: any[], days: number) {
  if (!dataPoints || dataPoints.length === 0) return { gmv: 0, aov: 0, transactions: 0 };

  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - days);

  const filteredPoints = dataPoints.filter(point => {
    const pointDate = new Date(point.date + 'T12:00:00');
    return pointDate >= startDate;
  });

  const totalGMV = filteredPoints.reduce((sum, point) => sum + point.gmv, 0);
  const totalTransactions = filteredPoints.reduce((sum, point) => sum + point.transactionCount, 0);
  const avgAOV = totalTransactions > 0 ? totalGMV / totalTransactions : 0;

  return { gmv: totalGMV, aov: avgAOV, transactions: totalTransactions };
}

// Helper function to get yesterday's metrics
function getYesterdayMetrics(dataPoints: any[]) {
  if (!dataPoints || dataPoints.length === 0) return { gmv: 0, aov: 0, transactions: 0 };

  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = yesterday.toISOString().split('T')[0];

  const yesterdayPoint = dataPoints.find(point => point.date === yesterdayStr);

  if (!yesterdayPoint) return { gmv: 0, aov: 0, transactions: 0 };

  return {
    gmv: yesterdayPoint.gmv,
    aov: yesterdayPoint.aov,
    transactions: yesterdayPoint.transactionCount
  };
}

interface ShopMyMetricsProps {
  analyticsData?: PlatformAnalytics;
  loading?: boolean;
  error?: string;
}

export function ShopMyMetrics({
  analyticsData,
  loading,
  error
}: ShopMyMetricsProps) {

  const yesterdayMetrics = React.useMemo(() => {
    if (!analyticsData?.dataPoints) return { gmv: 0, aov: 0, transactions: 0 };
    return getYesterdayMetrics(analyticsData.dataPoints);
  }, [analyticsData]);

  const last7DaysMetrics = React.useMemo(() => {
    if (!analyticsData?.dataPoints) return { gmv: 0, aov: 0, transactions: 0 };
    return calculatePeriodMetrics(analyticsData.dataPoints, 7);
  }, [analyticsData]);

  const last30DaysMetrics = React.useMemo(() => {
    if (!analyticsData?.dataPoints) return { gmv: 0, aov: 0, transactions: 0 };
    return calculatePeriodMetrics(analyticsData.dataPoints, 30);
  }, [analyticsData]);

  if (loading) {
    return (
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">--</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <div className="text-sm text-destructive">Error loading Shop My metrics: {error}</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Shop My Quick Stats</h3>
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
        {/* Yesterday's Metrics */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Yesterday's GMV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(yesterdayMetrics.gmv)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Yesterday's AOV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(yesterdayMetrics.aov)}</div>
          </CardContent>
        </Card>

        {/* 7 Days Metrics */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              7 Days GMV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(last7DaysMetrics.gmv)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              7 Days AOV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(last7DaysMetrics.aov)}</div>
          </CardContent>
        </Card>

        {/* 30 Days Metrics */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              30 Days GMV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(last30DaysMetrics.gmv)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              30 Days AOV
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(last30DaysMetrics.aov)}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

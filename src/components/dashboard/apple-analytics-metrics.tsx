"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>Header,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Info } from "lucide-react";
import { AppleAnalyticsData } from '@/hooks/useAppleAnalytics';
import { formatLargeNumber } from '@/lib/analytics-utils';

interface AppleAnalyticsMetricsProps {
  analyticsData?: AppleAnalyticsData;
  loading?: boolean;
  error?: string;
}

// Colors for pie charts
const DEVICE_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];
const TERRITORY_COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff', '#ff0000', '#0000ff', '#ffff00'];

export function AppleAnalyticsMetrics({
  analyticsData,
  loading,
  error
}: AppleAnalyticsMetricsProps) {

  if (loading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Apple Analytics Metrics</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Loading...
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Apple Analytics Metrics</h3>
        <div className="text-sm text-destructive">Error loading metrics: {error}</div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Apple Analytics Metrics</h3>
        <div className="text-sm text-muted-foreground">No data available</div>
      </div>
    );
  }

  const { metrics } = analyticsData;

  // Prepare device data for pie chart
  const deviceData = metrics.deviceBreakdown.map((device, index) => ({
    name: device.device_type,
    value: device.installs,
    percentage: ((device.installs / metrics.totalInstalls) * 100).toFixed(1)
  }));

  // Prepare territory data for pie chart (top 10)
  const territoryData = metrics.topTerritories.slice(0, 10).map((territory, index) => ({
    name: territory.territory,
    value: territory.installs,
    percentage: ((territory.installs / metrics.totalInstalls) * 100).toFixed(1)
  }));

  return (
    <div className="space-y-6">

      {/* Pie Charts Side by Side */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Device Breakdown Pie Chart */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Device Breakdown</h3>
          <Card>
            <CardContent className="pt-6">
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={deviceData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={2}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {deviceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={DEVICE_COLORS[index % DEVICE_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number, name: string) => [
                      `${formatLargeNumber(value)} installs (${deviceData.find(d => d.name === name)?.percentage}%)`,
                      name
                    ]}
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                      color: 'hsl(var(--card-foreground))'
                    }}
                    labelStyle={{
                      color: 'hsl(var(--card-foreground))'
                    }}
                    itemStyle={{
                      color: 'hsl(var(--card-foreground))'
                    }}
                  />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    formatter={(value, entry) => (
                      <span style={{ color: 'hsl(var(--foreground))' }}>
                        {value} ({deviceData.find(d => d.name === value)?.percentage}%)
                      </span>
                    )}
                    iconType="circle"
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Territories Pie Chart */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Top Territories</h3>
          <Card>
            <CardContent className="pt-6">
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={territoryData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={2}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {territoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={TERRITORY_COLORS[index % TERRITORY_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number, name: string) => [
                      `${formatLargeNumber(value)} installs (${territoryData.find(d => d.name === name)?.percentage}%)`,
                      name
                    ]}
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                      color: 'hsl(var(--card-foreground))'
                    }}
                    labelStyle={{
                      color: 'hsl(var(--card-foreground))'
                    }}
                    itemStyle={{
                      color: 'hsl(var(--card-foreground))'
                    }}
                  />
                  <Legend
                    verticalAlign="bottom"
                    height={36}
                    formatter={(value, entry) => (
                      <span style={{ color: 'hsl(var(--foreground))' }}>
                        {value} ({territoryData.find(d => d.name === value)?.percentage}%)
                      </span>
                    )}
                    iconType="circle"
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

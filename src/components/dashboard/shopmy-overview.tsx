"use client";

import * as React from "react";
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts";

import { useIsMobile } from "@/hooks/use-mobile";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { PlatformAnalytics } from '@/lib/analytics-utils';
import { formatCurrency, formatLargeNumber } from '@/lib/analytics-utils';

export const description = "An interactive area chart displaying ShopMy analytics over time.";

const chartConfig = {
  gmv: {
    label: "GMV",
    color: "#f59e0b",
  },
  aov: {
    label: "AOV",
    color: "#8b5cf6",
  },
  revenue: {
    label: "Revenue",
    color: "#10b981",
  },
} satisfies ChartConfig;

interface ShopMyOverviewProps {
  analyticsData?: PlatformAnalytics;
  loading?: boolean;
  error?: string;
  onTimeRangeChange?: (timeRange: string) => void;
  timeRange?: string;
}

export function ShopMyOverview({
  analyticsData,
  loading,
  error,
  onTimeRangeChange,
  timeRange = "all"
}: ShopMyOverviewProps) {
  const isMobile = useIsMobile();
  const [metric, setMetric] = React.useState<"gmv" | "aov" | "revenue">("gmv");

  const chartData = React.useMemo(() => {
    if (!analyticsData?.dataPoints) return [];

    return analyticsData.dataPoints.map(point => ({
      date: point.date,
      gmv: point.gmv,
      aov: point.aov,
      revenue: point.revenue,
      transactionCount: point.transactionCount,
      value: metric === "gmv" ? point.gmv : metric === "aov" ? point.aov : point.revenue
    }));
  }, [analyticsData, metric]);

  const totalValue = React.useMemo(() => {
    if (!analyticsData) return 0;
    return metric === "gmv" ? analyticsData.totalGMV : metric === "aov" ? analyticsData.totalAOV : analyticsData.totalRevenue;
  }, [analyticsData, metric]);

  const displayValue = React.useMemo(() => {
    if (metric === "gmv") {
      return formatCurrency(totalValue);
    } else {
      return formatCurrency(totalValue);
    }
  }, [totalValue, metric]);



  if (loading) {
    return (
      <Card className="@container/card w-full">
        <CardHeader>
          <CardTitle className="text-base font-medium sm:text-lg">
            ShopMy Overview
          </CardTitle>
          <CardDescription>Loading analytics data...</CardDescription>
        </CardHeader>
        <CardContent className="flex h-[250px] items-center justify-center">
          <div className="text-sm text-muted-foreground">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="@container/card w-full">
        <CardHeader>
          <CardTitle className="text-base font-medium sm:text-lg">
            ShopMy Overview
          </CardTitle>
          <CardDescription className="text-destructive">
            Error loading data: {error}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex h-[250px] items-center justify-center">
          <div className="text-sm text-muted-foreground">Failed to load analytics</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="@container/card w-full">


      <CardHeader className="relative flex-row items-start space-y-0 pb-0">
        <div className="flex-1">
          <CardTitle className="text-base font-medium sm:text-lg">
            ShopMy Overview
          </CardTitle>
          <CardDescription className="mt-1 text-sm text-muted-foreground">
            {metric === "gmv" ? "Gross Merchandise Value" : metric === "aov" ? "Average Order Value" : "Total Revenue"} for the selected period
          </CardDescription>
          <div className="mt-1 flex items-baseline gap-1.5">
            <span className="text-2xl font-bold leading-none sm:text-3xl">
              {displayValue}
            </span>
            {analyticsData && (
              <span className="text-sm text-muted-foreground">
                ({analyticsData.totalTransactions} transactions)
              </span>
            )}
          </div>
        </div>
        <div className="absolute right-2 top-2 flex items-center gap-2 sm:right-4 sm:top-4">
          <Select value={timeRange} onValueChange={(value) => {
            onTimeRangeChange?.(value);
          }}>
            <SelectTrigger
              className="flex h-8 items-center rounded-lg border bg-background px-3 text-xs data-[state=open]:border-muted"
              aria-label="Select time range"
            >
              <SelectValue placeholder="All time" />
            </SelectTrigger>
            <SelectContent align="end" className="rounded-xl">
              <SelectItem value="all" className="relative flex w-full cursor-default select-none items-center rounded-lg py-2 pl-8 pr-2 text-xs outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                All time
              </SelectItem>
              <SelectItem value="since-launch" className="relative flex w-full cursor-default select-none items-center rounded-lg py-2 pl-8 pr-2 text-xs outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                Since launch
              </SelectItem>
              <SelectItem value="90d" className="relative flex w-full cursor-default select-none items-center rounded-lg py-2 pl-8 pr-2 text-xs outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                Last 3 months
              </SelectItem>
              <SelectItem value="30d" className="relative flex w-full cursor-default select-none items-center rounded-lg py-2 pl-8 pr-2 text-xs outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                Last 30 days
              </SelectItem>
              <SelectItem value="7d" className="relative flex w-full cursor-default select-none items-center rounded-lg py-2 pl-8 pr-2 text-xs outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                Last 7 days
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="px-1 pt-2 sm:px-4 sm:pt-4">
        <div className="mb-4 flex justify-center">
          <ToggleGroup
            type="single"
            value={metric}
            onValueChange={(value) => value && setMetric(value as "gmv" | "aov" | "revenue")}
            className="grid w-fit grid-cols-3"
          >
            <ToggleGroupItem value="gmv" className="text-xs">
              GMV
            </ToggleGroupItem>
            <ToggleGroupItem value="aov" className="text-xs">
              AOV
            </ToggleGroupItem>
            <ToggleGroupItem value="revenue" className="text-xs">
              REV
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[180px] w-full sm:h-[200px]"
        >
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="fillShopMyMetric" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={chartConfig[metric].color}
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor={chartConfig[metric].color}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} strokeDasharray="3 3" className="stroke-muted"/>
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                // Parse date correctly to avoid timezone issues
                const date = new Date(value + 'T12:00:00');
                return isMobile
                  ? date.toLocaleDateString("en-US", { month: "short" })
                  : date.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
              }}
            />
            <ChartTooltip
              cursor={true}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    // Parse date correctly to avoid timezone issues
                    return new Date(value + 'T12:00:00').toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  formatter={(value, _name, props) => {
                    const transactionCount = props.payload?.transactionCount || 0;
                    return [
                      <div key="metric" className="flex flex-col">
                        <span>{formatCurrency(value as number)}</span>
                        <span className="text-xs text-muted-foreground">
                          {transactionCount} transaction{transactionCount !== 1 ? 's' : ''}
                        </span>
                      </div>,
                      metric === "gmv" ? "GMV" : "AOV"
                    ];
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="value"
              type="natural"
              fill="url(#fillShopMyMetric)"
              stroke={chartConfig[metric].color}
              stackId="a"
              name={metric === "gmv" ? "GMV" : "AOV"}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowUpCircleIcon,
  BarChartIcon,
  CameraIcon,
  ClipboardListIcon,
  DatabaseIcon,
  FileCodeIcon,
  FileIcon,
  FileTextIcon,
  FolderIcon,
  HelpCircleIcon,
  LayoutDashboardIcon,
  LinkIcon,
  ListIcon,
  SearchIcon,
  SettingsIcon,
  TableIcon,
  UsersIcon,
} from "lucide-react";
import { createBrowserClient } from "@supabase/ssr";

// Import sibling components from the same directory
import { NavDocuments } from "./nav-documents";
import { NavMain } from "./nav-main";
import { NavSecondary } from "./nav-secondary";
import { NavUser } from "./nav-user";
import type { User } from "@supabase/supabase-js";

// Import UI components from the main UI directory
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Card } from "@/components/ui/card";

// Navigation data configuration
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    { title: "Dashboard", url: "/home", icon: LayoutDashboardIcon },
    { title: "Metrics", url: "/metrics", icon: BarChartIcon },
    { title: "Apple Analytics", url: "/apple-analytics", icon: CameraIcon },
    { title: "Affiliates", url: "/affiliates", icon: UsersIcon },
    { title: "Spreadsheet", url: "/spreadsheet", icon: FileTextIcon },
    { title: "ShopmyUpload", url: "/upload", icon: ArrowUpCircleIcon },
    { title: "Linkbuilder", url: "/linkbuilder", icon: LinkIcon },
    { title: "Affiliate Table", url: "/affiliate-table", icon: TableIcon },
  ],
  navClouds: [],
  navSecondary: [],
  documents: [
    { name: "Data Library", url: "#", icon: DatabaseIcon },
    { name: "Reports", url: "#", icon: ClipboardListIcon },
    { name: "Word Assistant", url: "#", icon: FileIcon },
  ],
};

// Define a simpler user type for the sidebar
type SidebarUser = {
  name: string;
  email: string;
  avatar: string | null;
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  const [currentUser, setCurrentUser] = useState<SidebarUser | null>(null);
  const [loadingUser, setLoadingUser] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      setLoadingUser(true);
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error) {
        console.error("Error fetching user for sidebar:", error);
        // Handle error appropriately, maybe show default state
        setCurrentUser(null);
      } else if (user) {
        // Use user metadata directly, prioritizing Google's avatar URL
        // Common keys for Google avatar in Supabase metadata are 'avatar_url' or 'picture'
        const avatarUrl =
          user.user_metadata?.avatar_url || user.user_metadata?.picture || null;
        const fullName =
          user.user_metadata?.full_name ||
          user.user_metadata?.name ||
          user.email?.split("@")[0] ||
          "User";

        setCurrentUser({
          email: user.email || "No email",
          name: fullName,
          avatar: avatarUrl,
        });
      } else {
        setCurrentUser(null); // No user logged in
      }
      setLoadingUser(false);
    };

    fetchUser();

    // Optional: Listen for auth changes to update sidebar if needed
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        fetchUser(); // Re-fetch user data on auth changes
      }
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase]);

  // Default user structure for NavUser if currentUser is null or loading
  const userForNav: SidebarUser = currentUser ?? {
    name: "Loading...",
    email: "...",
    avatar: null,
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <Card className="flex justify-center p-2 m-2 bg-sidebar shadow-md">
          <Link href="/home" className="flex items-center gap-2">
            <Image
              src="/logo.png"
              alt="Logo"
              width={48}
              height={48}
              className="h-12 w-auto"
            />
          </Link>
        </Card>
      </SidebarHeader>

      <Separator className="border-t border-muted-foreground/30" />

      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>

      <Separator className="my-2 border-t border-muted-foreground/30" />

      <SidebarFooter>
        {!loadingUser && <NavUser user={userForNav} />}
        {loadingUser && (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Loading user...
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}

"use client";

import React from "react";
/** @jsxImportSource react */
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronDownIcon,
  ColumnsIcon,
  ExternalLinkIcon,
  MoreVerticalIcon,
  RefreshCwIcon,
  FilterIcon,
} from "lucide-react";
import { SocialIcon } from 'react-social-icons';

import { useTransactions } from "@/hooks/useTransactions";
import { TransactionDisplayData } from "@/types/database";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Use the TransactionDisplayData type from our database types
export type SpreadsheetData = TransactionDisplayData;

const platformDisplayDetails: { [key: string]: { name: string; url: string } } = {
  strackr: { name: 'Strackr', url: 'https://strackr.com' },
  x: { name: 'X.com', url: 'https://x.com' },
  twitter: { name: 'Twitter', url: 'https://twitter.com' },
  linkedin: { name: 'LinkedIn', url: 'https://linkedin.com' },
  facebook: { name: 'Facebook', url: 'https://facebook.com' },
  instagram: { name: 'Instagram', url: 'https://instagram.com' },
  tiktok: { name: 'TikTok', url: 'https://tiktok.com' },
  reddit: { name: 'Reddit', url: 'https://reddit.com' },
};

// Status badge colors
const statusColors: { [key: string]: string } = {
  confirmed: "bg-green-100 text-green-800",
  pending: "bg-yellow-100 text-yellow-800",
  declined: "bg-red-100 text-red-800",
  cancelled: "bg-gray-100 text-gray-800",
};

export const columns: ColumnDef<SpreadsheetData>[] = [
  {
    accessorKey: "date",
    header: "Date",
    cell: ({ row }) => {
      const dateString = row.getValue("date") as string;
      const date = new Date(dateString);
      const formattedDate = !isNaN(date.getTime()) ? formatDate(date) : 'Invalid Date';
      return <div>{formattedDate}</div>;
    },
  },
  {
    accessorKey: "platform",
    header: "Platform",
    cell: ({ row }) => {
      const platformKey = row.getValue("platform") as string | null;
      const details = platformKey ? platformDisplayDetails[platformKey.toLowerCase()] : null;
      return (
        <div className="flex items-center gap-2">
          {details ? (
            <SocialIcon url={details.url} style={{ height: 20, width: 20 }} />
          ) : (
            <div className="h-5 w-5 bg-gray-300 rounded-full"></div>
          )}
          <span>{details ? details.name : platformKey ?? 'Unknown'}</span>
        </div>
      );
    },
    filterFn: (row, id, value) => {
      const platformKey = row.getValue(id) as string | null;
      return platformKey ? value.includes(platformKey) : false;
    },
  },
  {
    accessorKey: "merchant",
    header: "Merchant",
    cell: ({ row }) => {
      return <div>{row.getValue("merchant")}</div>;
    },
  },
  {
    accessorKey: "network",
    header: "Network",
    cell: ({ row }) => {
      return <div className="text-sm text-gray-600">{row.getValue("network")}</div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const colorClass = statusColors[status.toLowerCase()] || "bg-gray-100 text-gray-800";
      return (
        <Badge className={`${colorClass} capitalize`}>
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "order_amount",
    header: "Order Amount",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("order_amount") as string);
      const currency = row.original.currency || 'USD';
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount);
      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: "commission_amount",
    header: "Commission",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("commission_amount") as string);
      const currency = row.original.currency || 'USD';
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
      }).format(amount);
      return <div className="text-right font-medium text-green-600">{formatted}</div>;
    },
  },
  {
    accessorKey: "order_id",
    header: "Order ID",
    cell: ({ row }) => {
      const orderId = row.getValue("order_id") as string;
      return (
        <div className="text-sm font-mono max-w-[120px] truncate" title={orderId}>
          {orderId}
        </div>
      );
    },
  },
];

interface SpreadsheetDataTableProps {
  initialFilters?: {
    platform?: string;
    merchant?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  };
}

export function SpreadsheetDataTable({ initialFilters }: SpreadsheetDataTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Use the custom hook to fetch transaction data
  const {
    data,
    loading,
    error,
    totalCount,
    currentPage,
    totalPages,
    refetch,
    setFilters,
    setPage,
    nextPage,
    previousPage,
    canNextPage,
    canPreviousPage
  } = useTransactions({
    ...initialFilters,
    limit: 100,
    autoFetch: true
  });

  // Update filters when initialFilters change
  React.useEffect(() => {
    if (initialFilters) {
      setFilters(initialFilters);
    }
  }, [initialFilters, setFilters]);

  // Table container ref for styling
  const tableContainerRef = React.useRef<HTMLDivElement>(null);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Handle loading and error states
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Transaction Data</CardTitle>
          <CardDescription>Error loading transaction data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-red-600 mb-4">Error: {error}</p>
            <Button onClick={refetch} variant="outline">
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Transaction Data</CardTitle>
            <CardDescription>
              Real-time affiliate marketing transaction data from your Supabase database.
              {totalCount > 0 && ` Showing ${data.length} of ${totalCount} transactions.`}
            </CardDescription>
          </div>
          <Button
            onClick={refetch}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCwIcon className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4 gap-4">
          <Input
            placeholder="Filter merchants..."
            value={(table.getColumn("merchant")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("merchant")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <ColumnsIcon className="mr-2 h-4 w-4" /> Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {typeof column.columnDef.header === 'string' ? column.columnDef.header : column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div
          ref={tableContainerRef}
          className="rounded-md border overflow-auto max-h-[600px]"
        >
          <Table>
            <TableHeader className="sticky top-0 bg-white z-10">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} colSpan={header.colSpan}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                <>
                  {table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  )}
                </>
              ) : loading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    <div className="flex items-center justify-center">
                      <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
                      Loading transactions...
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No transactions found. Try adjusting your filters.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="py-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {totalCount > 0 && (
                <>
                  Page {currentPage} of {totalPages} ({totalCount} total transactions)
                </>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {/* Page number input */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Go to page:</span>
                <Input
                  type="number"
                  min={1}
                  max={totalPages}
                  value={currentPage}
                  onChange={(e) => {
                    const page = parseInt(e.target.value);
                    if (page >= 1 && page <= totalPages) {
                      setPage(page);
                    }
                  }}
                  className="w-16 h-8 text-center"
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={previousPage}
                disabled={!canPreviousPage || loading}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={nextPage}
                disabled={!canNextPage || loading}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${month}/${day}/${year}`;
}

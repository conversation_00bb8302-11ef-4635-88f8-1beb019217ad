"use client";

import * as React from "react";
import { CalendarIcon, FilterIcon, XIcon } from "lucide-react";
import { dateToLocalDateString, formatDateForDisplay, safeStringToDate } from "@/lib/date-utils";

import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { usePlatformFilterOptions } from "@/hooks/usePlatformFilterOptions";
import { useIsMobile } from "@/hooks/use-mobile";

interface PlatformFilters {
  merchant?: string;
  status?: string;
  network?: string;
  transactionType?: string;
  startDate?: string;
  endDate?: string;
}

interface PlatformFiltersProps {
  platform: 'strackr' | 'shopmy';
  onFiltersChange: (filters: PlatformFilters) => void;
  initialFilters?: PlatformFilters;
}

export function PlatformFilters({ platform, onFiltersChange, initialFilters = {} }: PlatformFiltersProps) {
  const isMobile = useIsMobile();
  const { data: filterOptions, loading: filterOptionsLoading } = usePlatformFilterOptions(platform);
  const [filters, setFilters] = React.useState(initialFilters);
  const [startDate, setStartDate] = React.useState<Date | undefined>(
    initialFilters.startDate ? safeStringToDate(initialFilters.startDate) : undefined
  );
  const [endDate, setEndDate] = React.useState<Date | undefined>(
    initialFilters.endDate ? safeStringToDate(initialFilters.endDate) : undefined
  );

  // Update filters when they change
  React.useEffect(() => {
    const updatedFilters = {
      ...filters,
      startDate: startDate ? dateToLocalDateString(startDate) : undefined,
      endDate: endDate ? dateToLocalDateString(endDate) : undefined,
    };
    onFiltersChange(updatedFilters);
  }, [filters.merchant, filters.status, filters.network, filters.transactionType, startDate, endDate]);

  const handleFilterChange = (key: string, value: string | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setStartDate(undefined);
    setEndDate(undefined);
  };

  const activeFiltersCount = Object.values({
    ...filters,
    startDate,
    endDate
  }).filter(Boolean).length;

  const platformName = platform === 'strackr' ? 'Strackr' : 'ShopMy';

  return (
    <div className="border border-border rounded-lg p-3 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FilterIcon className="h-4 w-4" />
          <span className="text-sm font-medium">{platformName} Filters</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </div>
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-6 px-2 text-xs"
          >
            <XIcon className="h-3 w-3 mr-1" />
            Clear
          </Button>
        )}
      </div>

      <div className={`grid gap-2 ${isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3'}`}>
        {/* Merchant Filter */}
        <div className="space-y-1">
          <Label className="text-xs">Merchant</Label>
          <Select
            value={filters.merchant || undefined}
            onValueChange={(value) => handleFilterChange("merchant", value === "all" ? undefined : value)}
            disabled={filterOptionsLoading}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filterOptions?.merchants.map((merchant) => (
                <SelectItem key={merchant.value} value={merchant.value}>
                  {merchant.label} ({merchant.count})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-1">
          <Label className="text-xs">Status</Label>
          <Select
            value={filters.status || undefined}
            onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
            disabled={filterOptionsLoading}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filterOptions?.statuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label} ({status.count})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Transaction Type Filter */}
        <div className="space-y-1">
          <Label className="text-xs">Type</Label>
          <Select
            value={filters.transactionType || undefined}
            onValueChange={(value) => handleFilterChange("transactionType", value === "all" ? undefined : value)}
            disabled={filterOptionsLoading}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filterOptions?.transactionTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label} ({type.count})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Network Filter */}
        <div className="space-y-1">
          <Label className="text-xs">Network</Label>
          <Select
            value={filters.network || undefined}
            onValueChange={(value) => handleFilterChange("network", value === "all" ? undefined : value)}
            disabled={filterOptionsLoading}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filterOptions?.networks.map((network) => (
                <SelectItem key={network.value} value={network.value}>
                  {network.label} ({network.count})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Start Date Filter */}
        <div className="space-y-1">
          <Label className="text-xs">Start Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-8 w-full justify-start text-left font-normal text-xs"
              >
                <CalendarIcon className="mr-1 h-3 w-3" />
                {startDate ? formatDateForDisplay(startDate) : "Pick date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                autoFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* End Date Filter */}
        <div className="space-y-1">
          <Label className="text-xs">End Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-8 w-full justify-start text-left font-normal text-xs"
              >
                <CalendarIcon className="mr-1 h-3 w-3" />
                {endDate ? formatDateForDisplay(endDate) : "Pick date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                autoFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
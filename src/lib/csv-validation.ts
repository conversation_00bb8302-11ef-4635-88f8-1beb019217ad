/**
 * Comprehensive CSV Validation Utilities
 * 
 * Provides detailed validation for CSV files including format checking,
 * data validation, and business rule enforcement.
 */

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  summary: ValidationSummary;
}

export interface ValidationError {
  type: 'CRITICAL' | 'ERROR' | 'WARNING';
  code: string;
  message: string;
  rowNumber?: number;
  columnName?: string;
  value?: string;
  suggestion?: string;
}

export interface ValidationWarning {
  type: 'FORMAT' | 'DATA' | 'BUSINESS';
  message: string;
  rowNumber?: number;
  columnName?: string;
  value?: string;
}

export interface ValidationSummary {
  totalRows: number;
  validRows: number;
  errorRows: number;
  warningRows: number;
  duplicateRows: number;
  emptyRows: number;
}

/**
 * Comprehensive CSV validator class
 */
export class CsvValidator {
  private errors: ValidationError[] = [];
  private warnings: ValidationWarning[] = [];
  private summary: ValidationSummary = {
    totalRows: 0,
    validRows: 0,
    errorRows: 0,
    warningRows: 0,
    duplicateRows: 0,
    emptyRows: 0
  };

  /**
   * Validate complete CSV data
   */
  validateCsv(headers: string[], rows: string[][]): ValidationResult {
    this.reset();
    
    // Validate headers
    this.validateHeaders(headers);
    
    // Validate data rows
    this.validateDataRows(headers, rows);
    
    // Check for duplicates
    this.checkDuplicates(headers, rows);
    
    return {
      isValid: this.errors.filter(e => e.type === 'CRITICAL' || e.type === 'ERROR').length === 0,
      errors: this.errors,
      warnings: this.warnings,
      summary: this.summary
    };
  }

  private reset() {
    this.errors = [];
    this.warnings = [];
    this.summary = {
      totalRows: 0,
      validRows: 0,
      errorRows: 0,
      warningRows: 0,
      duplicateRows: 0,
      emptyRows: 0
    };
  }

  private validateHeaders(headers: string[]) {
    const requiredColumns = ['Date', 'Merchant', 'Order Amount USD', 'Status'];
    const optionalColumns = ['Domain', 'Commission USD', 'Code Used'];
    const allValidColumns = [...requiredColumns, ...optionalColumns];

    // Check for required columns
    for (const required of requiredColumns) {
      const found = headers.some(header => 
        header.trim().toLowerCase() === required.toLowerCase()
      );
      
      if (!found) {
        this.addError('CRITICAL', 'MISSING_REQUIRED_COLUMN', 
          `Missing required column: ${required}`, undefined, undefined, undefined,
          `Add a column named exactly "${required}"`);
      }
    }

    // Check for unknown columns
    for (let i = 0; i < headers.length; i++) {
      const header = headers[i].trim();
      const isValid = allValidColumns.some(valid => 
        valid.toLowerCase() === header.toLowerCase()
      );
      
      if (!isValid && header !== '') {
        this.addWarning('FORMAT', 
          `Unknown column "${header}" will be ignored`, undefined, header);
      }
    }

    // Check for duplicate headers
    const headerCounts = new Map<string, number>();
    headers.forEach(header => {
      const normalized = header.trim().toLowerCase();
      headerCounts.set(normalized, (headerCounts.get(normalized) || 0) + 1);
    });

    headerCounts.forEach((count, header) => {
      if (count > 1) {
        this.addError('ERROR', 'DUPLICATE_HEADER',
          `Duplicate column header: ${header}`, undefined, header);
      }
    });
  }

  private validateDataRows(headers: string[], rows: string[][]) {
    this.summary.totalRows = rows.length;

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const rowNumber = i + 2; // +2 because CSV is 1-indexed and we skip header
      let hasErrors = false;
      let hasWarnings = false;

      // Check if row is empty
      if (this.isEmptyRow(row)) {
        this.summary.emptyRows++;
        continue;
      }

      // Validate each required field
      hasErrors = this.validateRowData(headers, row, rowNumber) || hasErrors;

      // Update counters
      if (hasErrors) {
        this.summary.errorRows++;
      } else if (hasWarnings) {
        this.summary.warningRows++;
      } else {
        this.summary.validRows++;
      }
    }
  }

  private validateRowData(headers: string[], row: string[], rowNumber: number): boolean {
    let hasErrors = false;

    // Get column indices
    const dateIndex = this.findColumnIndex(headers, 'Date');
    const merchantIndex = this.findColumnIndex(headers, 'Merchant');
    const amountIndex = this.findColumnIndex(headers, 'Order Amount USD');
    const statusIndex = this.findColumnIndex(headers, 'Status');
    const commissionIndex = this.findColumnIndex(headers, 'Commission USD');

    // Validate Date
    if (dateIndex >= 0) {
      const dateValue = row[dateIndex]?.trim();
      if (!dateValue) {
        this.addError('ERROR', 'MISSING_DATE', 'Date is required', rowNumber, 'Date');
        hasErrors = true;
      } else if (!this.isValidDate(dateValue)) {
        this.addError('ERROR', 'INVALID_DATE', 
          `Invalid date format: ${dateValue}`, rowNumber, 'Date', dateValue,
          'Use MM/DD/YYYY format (e.g., 06/24/2025)');
        hasErrors = true;
      }
    }

    // Validate Merchant
    if (merchantIndex >= 0) {
      const merchantValue = row[merchantIndex]?.trim();
      if (!merchantValue) {
        this.addError('ERROR', 'MISSING_MERCHANT', 'Merchant name is required', rowNumber, 'Merchant');
        hasErrors = true;
      } else if (merchantValue.length > 255) {
        this.addWarning('DATA', 'Merchant name is very long and may be truncated', rowNumber, 'Merchant', merchantValue);
      }
    }

    // Validate Order Amount
    if (amountIndex >= 0) {
      const amountValue = row[amountIndex]?.trim();
      if (!amountValue) {
        this.addError('ERROR', 'MISSING_AMOUNT', 'Order amount is required', rowNumber, 'Order Amount USD');
        hasErrors = true;
      } else if (!this.isValidAmount(amountValue)) {
        this.addError('ERROR', 'INVALID_AMOUNT', 
          `Invalid amount format: ${amountValue}`, rowNumber, 'Order Amount USD', amountValue,
          'Use numeric format without currency symbols (e.g., 161.99)');
        hasErrors = true;
      } else {
        const amount = this.parseAmount(amountValue);
        if (amount <= 0) {
          this.addError('ERROR', 'INVALID_AMOUNT_VALUE', 
            `Amount must be greater than 0: ${amountValue}`, rowNumber, 'Order Amount USD', amountValue);
          hasErrors = true;
        } else if (amount > 1000000) {
          this.addWarning('DATA', 'Very large order amount detected', rowNumber, 'Order Amount USD', amountValue);
        }
      }
    }

    // Validate Status
    if (statusIndex >= 0) {
      const statusValue = row[statusIndex]?.trim().toLowerCase();
      const validStatuses = ['active', 'pending', 'declined', 'cancelled', 'confirmed', 'failed'];
      if (!statusValue) {
        this.addError('ERROR', 'MISSING_STATUS', 'Status is required', rowNumber, 'Status');
        hasErrors = true;
      } else if (!validStatuses.includes(statusValue)) {
        this.addError('ERROR', 'INVALID_STATUS', 
          `Invalid status: ${statusValue}`, rowNumber, 'Status', statusValue,
          `Use one of: ${validStatuses.join(', ')}`);
        hasErrors = true;
      }
    }

    // Validate Commission (optional)
    if (commissionIndex >= 0) {
      const commissionValue = row[commissionIndex]?.trim();
      if (commissionValue && commissionValue !== '-' && !this.isValidAmount(commissionValue)) {
        this.addError('ERROR', 'INVALID_COMMISSION', 
          `Invalid commission format: ${commissionValue}`, rowNumber, 'Commission USD', commissionValue,
          'Use numeric format or "-" for no commission');
        hasErrors = true;
      }
    }

    return hasErrors;
  }

  private checkDuplicates(headers: string[], rows: string[][]) {
    const dateIndex = this.findColumnIndex(headers, 'Date');
    const merchantIndex = this.findColumnIndex(headers, 'Merchant');
    const amountIndex = this.findColumnIndex(headers, 'Order Amount USD');

    if (dateIndex < 0 || merchantIndex < 0 || amountIndex < 0) {
      return; // Can't check duplicates without required fields
    }

    const seen = new Set<string>();
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const rowNumber = i + 2;

      if (this.isEmptyRow(row)) continue;

      const date = row[dateIndex]?.trim();
      const merchant = row[merchantIndex]?.trim();
      const amount = row[amountIndex]?.trim();

      if (date && merchant && amount) {
        const key = `${date}|${merchant.toLowerCase()}|${amount}`;
        
        if (seen.has(key)) {
          this.addWarning('BUSINESS', 
            'Potential duplicate transaction detected', rowNumber, undefined, 
            `${date} - ${merchant} - ${amount}`);
          this.summary.duplicateRows++;
        } else {
          seen.add(key);
        }
      }
    }
  }

  private findColumnIndex(headers: string[], columnName: string): number {
    return headers.findIndex(header => 
      header.trim().toLowerCase() === columnName.toLowerCase()
    );
  }

  private isEmptyRow(row: string[]): boolean {
    return row.every(cell => !cell || cell.trim() === '');
  }

  private isValidDate(dateString: string): boolean {
    // Check MM/DD/YYYY format
    const mmddyyyy = dateString.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
    if (mmddyyyy) {
      const [, month, day, year] = mmddyyyy;
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.getFullYear() == parseInt(year) &&
             date.getMonth() == parseInt(month) - 1 &&
             date.getDate() == parseInt(day);
    }
    return false;
  }

  private isValidAmount(amountString: string): boolean {
    if (!amountString || amountString.trim() === '' || amountString === '-') {
      return false;
    }
    
    // Remove currency symbols and whitespace
    const cleaned = amountString.replace(/[$,\s]/g, '');
    const amount = parseFloat(cleaned);
    return !isNaN(amount) && isFinite(amount);
  }

  private parseAmount(amountString: string): number {
    const cleaned = amountString.replace(/[$,\s]/g, '');
    return parseFloat(cleaned);
  }

  private addError(type: 'CRITICAL' | 'ERROR' | 'WARNING', code: string, message: string, 
                   rowNumber?: number, columnName?: string, value?: string, suggestion?: string) {
    this.errors.push({
      type,
      code,
      message,
      rowNumber,
      columnName,
      value,
      suggestion
    });
  }

  private addWarning(type: 'FORMAT' | 'DATA' | 'BUSINESS', message: string, 
                     rowNumber?: number, columnName?: string, value?: string) {
    this.warnings.push({
      type,
      message,
      rowNumber,
      columnName,
      value
    });
  }
}

/**
 * Quick validation function for basic CSV checks
 */
export function quickValidateCsv(fileContent: string): {
  isValid: boolean;
  errors: string[];
  rowCount: number;
} {
  try {
    const lines = fileContent.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return {
        isValid: false,
        errors: ['CSV must contain at least a header row and one data row'],
        rowCount: 0
      };
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const requiredColumns = ['Date', 'Merchant', 'Order Amount USD', 'Status'];
    const missingColumns = requiredColumns.filter(required => 
      !headers.some(header => header.toLowerCase() === required.toLowerCase())
    );

    if (missingColumns.length > 0) {
      return {
        isValid: false,
        errors: [`Missing required columns: ${missingColumns.join(', ')}`],
        rowCount: lines.length - 1
      };
    }

    return {
      isValid: true,
      errors: [],
      rowCount: lines.length - 1
    };

  } catch (error) {
    return {
      isValid: false,
      errors: ['Failed to parse CSV file'],
      rowCount: 0
    };
  }
}

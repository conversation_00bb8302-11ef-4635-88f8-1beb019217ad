/**
 * Duplicate Detection Utilities
 *
 * Advanced duplicate detection logic for CSV uploads to prevent
 * duplicate transactions from being inserted into the database.
 */

// Note: This utility expects a Supabase client to be passed in
import { NormalizedTransaction } from '@/types/database';

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingTransactionId?: string;
  matchType: 'exact' | 'fuzzy' | 'none';
  confidence: number; // 0-100
  matchedFields: string[];
}

export interface DuplicateDetectionOptions {
  strictMode?: boolean; // If true, requires exact matches
  fuzzyThreshold?: number; // Confidence threshold for fuzzy matching (0-100)
  checkFields?: string[]; // Fields to check for duplicates
  timeWindow?: number; // Days to look back for duplicates
}

/**
 * Advanced duplicate detection class
 */
export class DuplicateDetector {
  private supabase;
  private options: Required<DuplicateDetectionOptions>;

  constructor(supabaseClient: any, options: DuplicateDetectionOptions = {}) {
    this.supabase = supabaseClient;
    this.options = {
      strictMode: false,
      fuzzyThreshold: 85,
      checkFields: ['merchant_name', 'order_amount', 'transaction_date'],
      timeWindow: 30,
      ...options
    };
  }

  /**
   * Check if a transaction is a duplicate
   */
  async checkDuplicate(transaction: Partial<NormalizedTransaction>): Promise<DuplicateCheckResult> {
    try {
      // First, try exact match
      const exactMatch = await this.findExactMatch(transaction);
      if (exactMatch) {
        return {
          isDuplicate: true,
          existingTransactionId: exactMatch.id,
          matchType: 'exact',
          confidence: 100,
          matchedFields: this.options.checkFields
        };
      }

      // If strict mode, don't do fuzzy matching
      if (this.options.strictMode) {
        return {
          isDuplicate: false,
          matchType: 'none',
          confidence: 0,
          matchedFields: []
        };
      }

      // Try fuzzy matching
      const fuzzyMatch = await this.findFuzzyMatch(transaction);
      if (fuzzyMatch && fuzzyMatch.confidence >= this.options.fuzzyThreshold) {
        return {
          isDuplicate: true,
          existingTransactionId: fuzzyMatch.id,
          matchType: 'fuzzy',
          confidence: fuzzyMatch.confidence,
          matchedFields: fuzzyMatch.matchedFields
        };
      }

      return {
        isDuplicate: false,
        matchType: 'none',
        confidence: 0,
        matchedFields: []
      };

    } catch (error) {
      console.error('Error checking for duplicates:', error);
      // On error, assume not duplicate to avoid blocking valid transactions
      return {
        isDuplicate: false,
        matchType: 'none',
        confidence: 0,
        matchedFields: []
      };
    }
  }

  /**
   * Find exact match based on key fields
   */
  private async findExactMatch(transaction: Partial<NormalizedTransaction>): Promise<any> {
    if (!transaction.merchant_name || !transaction.order_amount || !transaction.transaction_date) {
      return null;
    }

    // Calculate time window
    const transactionDate = new Date(transaction.transaction_date);
    const startDate = new Date(transactionDate);
    startDate.setDate(startDate.getDate() - this.options.timeWindow);
    const endDate = new Date(transactionDate);
    endDate.setDate(endDate.getDate() + this.options.timeWindow);

    const { data, error } = await this.supabase
      .from('normalized_transactions')
      .select('id, merchant_name, order_amount, transaction_date, platform')
      .eq('platform', transaction.platform || 'shopmy')
      .eq('merchant_name', transaction.merchant_name)
      .eq('order_amount', transaction.order_amount)
      .gte('transaction_date', startDate.toISOString())
      .lte('transaction_date', endDate.toISOString())
      .limit(1);

    if (error) {
      throw error;
    }

    return data && data.length > 0 ? data[0] : null;
  }

  /**
   * Find fuzzy match using similarity algorithms
   */
  private async findFuzzyMatch(transaction: Partial<NormalizedTransaction>): Promise<{
    id: string;
    confidence: number;
    matchedFields: string[];
  } | null> {
    if (!transaction.merchant_name || !transaction.order_amount || !transaction.transaction_date) {
      return null;
    }

    // Calculate time window for fuzzy search
    const transactionDate = new Date(transaction.transaction_date);
    const startDate = new Date(transactionDate);
    startDate.setDate(startDate.getDate() - this.options.timeWindow);
    const endDate = new Date(transactionDate);
    endDate.setDate(endDate.getDate() + this.options.timeWindow);

    // Get potential matches within time window and similar amount
    const amountTolerance = transaction.order_amount * 0.01; // 1% tolerance
    const minAmount = transaction.order_amount - amountTolerance;
    const maxAmount = transaction.order_amount + amountTolerance;

    const { data, error } = await this.supabase
      .from('normalized_transactions')
      .select('id, merchant_name, order_amount, transaction_date, platform, commission_amount')
      .eq('platform', transaction.platform || 'shopmy')
      .gte('order_amount', minAmount)
      .lte('order_amount', maxAmount)
      .gte('transaction_date', startDate.toISOString())
      .lte('transaction_date', endDate.toISOString())
      .limit(50); // Limit to avoid performance issues

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      return null;
    }

    // Calculate similarity scores for each potential match
    let bestMatch: { id: string; confidence: number; matchedFields: string[] } | null = null;

    for (const candidate of data) {
      const similarity = this.calculateSimilarity(transaction, candidate);

      if (similarity.confidence > (bestMatch?.confidence || 0)) {
        bestMatch = {
          id: candidate.id,
          confidence: similarity.confidence,
          matchedFields: similarity.matchedFields
        };
      }
    }

    return bestMatch;
  }

  /**
   * Calculate similarity between two transactions
   */
  private calculateSimilarity(
    transaction: Partial<NormalizedTransaction>,
    candidate: any
  ): { confidence: number; matchedFields: string[] } {
    const scores: { [field: string]: number } = {};
    const matchedFields: string[] = [];

    // Merchant name similarity (weighted heavily)
    const merchantSimilarity = this.stringSimilarity(
      transaction.merchant_name || '',
      candidate.merchant_name || ''
    );
    scores.merchant = merchantSimilarity * 0.4; // 40% weight
    if (merchantSimilarity > 0.8) matchedFields.push('merchant_name');

    // Amount similarity (exact match gets full score)
    const amountDiff = Math.abs((transaction.order_amount || 0) - (candidate.order_amount || 0));
    const amountSimilarity = amountDiff === 0 ? 1 : Math.max(0, 1 - (amountDiff / (transaction.order_amount || 1)));
    scores.amount = amountSimilarity * 0.3; // 30% weight
    if (amountSimilarity > 0.95) matchedFields.push('order_amount');

    // Date similarity (same day gets full score)
    const transactionDate = new Date(transaction.transaction_date || '');
    const candidateDate = new Date(candidate.transaction_date || '');
    const daysDiff = Math.abs(transactionDate.getTime() - candidateDate.getTime()) / (1000 * 60 * 60 * 24);
    const dateSimilarity = daysDiff === 0 ? 1 : Math.max(0, 1 - (daysDiff / 7)); // 7-day tolerance
    scores.date = dateSimilarity * 0.2; // 20% weight
    if (daysDiff < 1) matchedFields.push('transaction_date');

    // Commission similarity (if available)
    if (transaction.commission_amount && candidate.commission_amount) {
      const commissionDiff = Math.abs(transaction.commission_amount - candidate.commission_amount);
      const commissionSimilarity = commissionDiff === 0 ? 1 : Math.max(0, 1 - (commissionDiff / transaction.commission_amount));
      scores.commission = commissionSimilarity * 0.1; // 10% weight
      if (commissionSimilarity > 0.95) matchedFields.push('commission_amount');
    }

    // Calculate overall confidence
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const confidence = Math.round(totalScore * 100);

    return { confidence, matchedFields };
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private stringSimilarity(str1: string, str2: string): number {
    const s1 = str1.toLowerCase().trim();
    const s2 = str2.toLowerCase().trim();

    if (s1 === s2) return 1;
    if (s1.length === 0 || s2.length === 0) return 0;

    // Levenshtein distance
    const matrix = Array(s2.length + 1).fill(null).map(() => Array(s1.length + 1).fill(null));

    for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= s2.length; j++) {
      for (let i = 1; i <= s1.length; i++) {
        const indicator = s1[i - 1] === s2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    const distance = matrix[s2.length][s1.length];
    const maxLength = Math.max(s1.length, s2.length);
    return 1 - (distance / maxLength);
  }

  /**
   * Batch check for duplicates (more efficient for large datasets)
   */
  async batchCheckDuplicates(
    transactions: Partial<NormalizedTransaction>[]
  ): Promise<DuplicateCheckResult[]> {
    const results: DuplicateCheckResult[] = [];

    // Process in smaller batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(transaction => this.checkDuplicate(transaction))
      );
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Get duplicate statistics for a set of transactions
   */
  getDuplicateStats(results: DuplicateCheckResult[]): {
    totalChecked: number;
    duplicatesFound: number;
    exactMatches: number;
    fuzzyMatches: number;
    averageConfidence: number;
  } {
    const duplicates = results.filter(r => r.isDuplicate);
    const exactMatches = duplicates.filter(r => r.matchType === 'exact').length;
    const fuzzyMatches = duplicates.filter(r => r.matchType === 'fuzzy').length;
    const totalConfidence = duplicates.reduce((sum, r) => sum + r.confidence, 0);

    return {
      totalChecked: results.length,
      duplicatesFound: duplicates.length,
      exactMatches,
      fuzzyMatches,
      averageConfidence: duplicates.length > 0 ? totalConfidence / duplicates.length : 0
    };
  }
}

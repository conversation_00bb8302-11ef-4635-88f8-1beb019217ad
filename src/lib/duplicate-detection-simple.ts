/**
 * Simple duplicate detection service for CSV uploads
 * Uses transaction fingerprints to detect exact duplicates
 */

import { NormalizedTransaction } from '@/types/database';
import { generateTransactionFingerprint } from './csv-mapping';

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingTransactionId?: string;
  fingerprint: string;
}

/**
 * Simple duplicate detector using fingerprints
 */
export class SimpleDuplicateDetector {
  private supabase;

  constructor(supabaseClient: any) {
    this.supabase = supabaseClient;
  }

  /**
   * Check if a transaction is a duplicate based on fingerprint
   */
  async checkDuplicate(transaction: Partial<NormalizedTransaction>): Promise<DuplicateCheckResult> {
    try {
      // Generate fingerprint for the transaction
      const fingerprint = generateTransactionFingerprint(transaction);

      // Check if this fingerprint already exists in the database
      const { data, error } = await this.supabase
        .from('normalized_transactions')
        .select('id')
        .eq('transaction_fingerprint', fingerprint)
        .limit(1);

      if (error) {
        console.error('Error checking for duplicates:', error);
        // On error, assume not duplicate to avoid blocking valid transactions
        return {
          isDuplicate: false,
          fingerprint
        };
      }

      if (data && data.length > 0) {
        return {
          isDuplicate: true,
          existingTransactionId: data[0].id,
          fingerprint
        };
      }

      return {
        isDuplicate: false,
        fingerprint
      };

    } catch (error) {
      console.error('Error in duplicate detection:', error);
      // On error, assume not duplicate to avoid blocking valid transactions
      return {
        isDuplicate: false,
        fingerprint: ''
      };
    }
  }

  /**
   * Batch check multiple transactions for duplicates
   */
  async checkBatchDuplicates(transactions: Partial<NormalizedTransaction>[]): Promise<DuplicateCheckResult[]> {
    const results: DuplicateCheckResult[] = [];

    // Generate fingerprints for all transactions
    const fingerprints = transactions.map(transaction => ({
      transaction,
      fingerprint: generateTransactionFingerprint(transaction)
    }));

    // Get all fingerprints that exist in the database
    const fingerprintValues = fingerprints.map(f => f.fingerprint);

    const { data: existingFingerprints, error } = await this.supabase
      .from('normalized_transactions')
      .select('id, transaction_fingerprint')
      .in('transaction_fingerprint', fingerprintValues);

    if (error) {
      console.error('Error in batch duplicate check:', error);
      // Return all as non-duplicates on error
      return fingerprints.map(f => ({
        isDuplicate: false,
        fingerprint: f.fingerprint
      }));
    }

    // Create a map of existing fingerprints
    const existingFingerprintMap = new Map();
    if (existingFingerprints) {
      existingFingerprints.forEach((item: any) => {
        existingFingerprintMap.set(item.transaction_fingerprint, item.id);
      });
    }

    // Check each transaction
    for (const { fingerprint } of fingerprints) {
      const existingId = existingFingerprintMap.get(fingerprint);
      results.push({
        isDuplicate: !!existingId,
        existingTransactionId: existingId,
        fingerprint
      });
    }

    return results;
  }
}

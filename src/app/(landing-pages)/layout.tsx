import React from "react";
import Link from "next/link";
import Image from "next/image";
import { hasEnvVars } from "@/supabase/client/check-env-vars";
import { EnvVarWarning } from "@/components/env-var-warning";
import HeaderAuth from "@/components/header-auth";
import { Footer } from "@/components/ui/large-name-footer";
import MainLayout from "@/components/main-layout";
import { Button } from "@/components/ui/button";
import { Sparkles, Menu } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";

// This layout applies only to the routes within the (mainPages) group
export default function MainPagesLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Define the header content here
  const header = (
    <div className="w-full flex justify-between items-center text-sm">
      <Link href={'/'} className="flex items-center ml-[10px]">
        <Image
          src="/logo.png"
          alt="Logo"
          width={60}
          height={60}
          className="filter invert brightness-0 dark:filter-none dark:invert-0"
        />
      </Link>
      {/* Buttons for larger screens (md and up) */}
      <div className="hidden md:flex items-center gap-3 mr-[22px]">
        <a
          href="https://forms.gle/SWk3hY1nKYFVDbGS9"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Button variant="outline" size="sm" className="text-sm gap-1.5">
            <Sparkles className="h-4 w-4" />
            Request a Feature
          </Button>
        </a>
        {!hasEnvVars ? <EnvVarWarning /> : <HeaderAuth />}
      </div>
      {/* Dropdown Menu for smaller screens (below md) */}
      <div className="md:hidden mr-[15px]">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Menu className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <a
                href="https://forms.gle/SWk3hY1nKYFVDbGS9"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1.5 w-full"
              >
                <Sparkles className="h-4 w-4" />
                Request a Feature
              </a>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {/* Render AuthButton items within the dropdown */}
            {!hasEnvVars ? (
              <DropdownMenuItem disabled>
                <EnvVarWarning /> {/* Show warning if needed */}
              </DropdownMenuItem>
            ) : (
              <HeaderAuth asDropdownItem={true} />
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  // Define the footer content here
  const footer = <Footer />;

  return (
    // Apply the MainLayout with header and footer only to these pages
    <MainLayout headerContent={header} footerContent={footer}>
      {children}
    </MainLayout>
  );
}

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button'; // Assuming Button component exists

export default function TermsOfServicePage() {
  return (
    <div className="w-full max-w-3xl mx-auto py-8 px-4">
       <div className="mb-6">
        <Link href="/sign-in">
          <Button variant="outline">&larr; Back</Button>
        </Link>
      </div>
      <h1 className="text-3xl font-bold mb-6">Terms of Service</h1>
      <p className="text-muted-foreground mb-4">Last updated: April 5, 2025</p> {/* Placeholder date */}

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Overview</h2>
        <p>
          Welcome to Twylo! These Terms of Service ("Terms") govern your use of the Twylo AI Marketing Copilot website, applications, and services (collectively, the "Service"). By accessing or using the Service, you agree to be bound by these Terms.
        </p>

        {/* Adapt content based on docs/terms.md */}
        <h2 className="text-2xl font-semibold">Service Description</h2>
        <p>
          Twylo provides an AI-powered platform designed to assist app developers and founders with marketing tasks, including social media automation, lead generation, and email outreach. The Service integrates with various third-party platforms based on user authorization.
        </p>

        <h2 className="text-2xl font-semibold">User Accounts</h2>
        <p>
          To use certain features of the Service, you must register for an account. You agree to provide accurate, current, and complete information during registration and keep your account information updated. You are responsible for safeguarding your password and for all activities that occur under your account.
        </p>

        <h2 className="text-2xl font-semibold">User Responsibilities</h2>
        <ul className="list-disc list-inside space-y-1 pl-4">
          <li>Comply with all applicable laws and regulations.</li>
          <li>Use the Service only for lawful purposes and in accordance with these Terms.</li>
          <li>Respect the intellectual property rights of others.</li>
          <li>Not misuse the Service, including attempting unauthorized access, distributing spam, or uploading malicious content.</li>
           <li>Ensure you have the necessary rights and permissions for any content you generate or share using the Service, including content posted to third-party platforms.</li>
        </ul>

        <h2 className="text-2xl font-semibold">Third-Party Services</h2>
        <p>
            The Service may allow you to connect with third-party services (e.g., social media platforms). Your use of such third-party services is governed by their respective terms and privacy policies. Twylo is not responsible for the operation or content of third-party services.
        </p>

         <h2 className="text-2xl font-semibold">Intellectual Property</h2>
        <p>
            The Service and its original content, features, and functionality are owned by Twylo and are protected by intellectual property laws. You retain ownership of the content you create or upload using the Service.
        </p>

        <h2 className="text-2xl font-semibold">Termination</h2>
        <p>
            We may suspend or terminate your access to the Service at any time, without prior notice or liability, for any reason, including breach of these Terms.
        </p>

        <h2 className="text-2xl font-semibold">Disclaimers and Limitation of Liability</h2>
         <p>
            The Service is provided "as is" without warranties of any kind. Twylo does not guarantee specific results from using the Service. To the fullest extent permitted by law, Twylo shall not be liable for any indirect, incidental, special, consequential, or punitive damages arising out of or related to your use of the Service.
        </p>

        <h2 className="text-2xl font-semibold">Governing Law</h2>
        <p>
            These Terms shall be governed by the laws of [Your Jurisdiction], without regard to its conflict of law provisions.
        </p>

         <h2 className="text-2xl font-semibold">Changes to Terms</h2>
         <p>
            We reserve the right to modify these Terms at any time. We will provide notice of significant changes. Your continued use of the Service after changes constitutes acceptance of the new Terms.
         </p>

        <h2 className="text-2xl font-semibold">Contact Information</h2>
        <p>
          For questions about these Terms, please contact us at: [Your Contact Email/Method]
        </p>
      </section>
    </div>
  );
}

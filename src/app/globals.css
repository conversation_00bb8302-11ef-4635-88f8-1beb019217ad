@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --radius: 0.5rem;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 10.6% 64.9%;
    --font-sans: var(--font-satoshi), sans-serif;
    --font-serif: Lora, serif;
    --font-mono: Roboto Mono, monospace;
    --font-satoshi: Satoshi, sans-serif;
  }

  .dashboard-theme {
    --background: 240 5% 18%;
    --foreground: 217.24 32.58% 95%;
    --card: 240 5% 22%;
    --card-foreground: 217.24 32.58% 95%;
    --popover: 240 5% 22%;
    --popover-foreground: 217.24 32.58% 95%;
    --primary: 235 89% 74%;
    --primary-foreground: 240 6% 10%;
    --secondary: 240 5% 30%;
    --secondary-foreground: 215 13.79% 90%;
    --muted: 240 5% 30%;
    --muted-foreground: 220 8.94% 60%;
    --accent: 292.5 44.44% 92.94%;
    --accent-foreground: 216.92 19.12% 26.67%;
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 5% 30%;
    --input: 240 5% 30%;
    --ring: 235 89% 74%;
    --chart-1: 235 89% 74%;
    --chart-2: 235 80% 68%;
    --chart-3: 235 70% 60%;
    --chart-4: 235 60% 52%;
    --chart-5: 235 50% 45%;
    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 217.24 32.58% 95%;
    --sidebar-primary: 235 89% 74%;
    --sidebar-primary-foreground: 240 6% 10%;
    --sidebar-accent: 292.5 44.44% 92.94%;
    --sidebar-accent-foreground: 216.92 19.12% 26.67%;
    --sidebar-border: 240 5% 30%;
    --sidebar-ring: 235 89% 74%;
    --font-sans: "Plus Jakarta Sans", sans-serif;
    --font-serif: "Lora", serif;
    --font-mono: "Roboto Mono", monospace;
    --radius: 1.25rem;
    --shadow-2xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
    --shadow-xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
    --shadow-sm:
      2px 2px 10px 4px hsl(240 4% 60% / 0.18),
      2px 1px 2px 3px hsl(240 4% 60% / 0.18);
    --shadow:
      2px 2px 10px 4px hsl(240 4% 60% / 0.18),
      2px 1px 2px 3px hsl(240 4% 60% / 0.18);
    --shadow-md:
      2px 2px 10px 4px hsl(240 4% 60% / 0.18),
      2px 2px 4px 3px hsl(240 4% 60% / 0.18);
    --shadow-lg:
      2px 2px 10px 4px hsl(240 4% 60% / 0.18),
      2px 4px 6px 3px hsl(240 4% 60% / 0.18);
    --shadow-xl:
      2px 2px 10px 4px hsl(240 4% 60% / 0.18),
      2px 8px 10px 3px hsl(240 4% 60% / 0.18);
    --shadow-2xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.45);
  }

  .dark .dashboard-theme {
    --background: 240 5% 12%;
    --foreground: 214.29 31.82% 91.37%;
    --card: 240 5% 16%;
    --card-foreground: 214.29 31.82% 91.37%;
    --popover: 240 5% 16%;
    --popover-foreground: 214.29 31.82% 91.37%;
    --primary: 235 89% 74%;
    --primary-foreground: 240 6% 5%;
    --secondary: 240 5% 25%;
    --secondary-foreground: 216 12.2% 83.92%;
    --muted: 240 5% 25%;
    --muted-foreground: 217.89 10.61% 64.9%;
    --accent: 240 5% 10%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 70% 45%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 5% 25%;
    --input: 240 5% 25%;
    --ring: 235 89% 74%;
    --chart-1: 235 89% 74%;
    --chart-2: 235 80% 68%;
    --chart-3: 235 70% 60%;
    --chart-4: 235 60% 52%;
    --chart-5: 235 50% 45%;
    --sidebar-background: 240 6% 5%;
    --sidebar-foreground: 214.29 31.82% 91.37%;
    --sidebar-primary: 235 89% 74%;
    --sidebar-primary-foreground: 240 6% 5%;
    --sidebar-accent: 240 5% 10%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 5% 25%;
    --sidebar-ring: 235 89% 74%;
    --font-sans: "Plus Jakarta Sans", sans-serif;
    --font-serif: "Lora", serif;
    --font-mono: "Roboto Mono", monospace;
    --radius: 1.25rem;
    --shadow-2xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
    --shadow-xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
    --shadow-sm:
      2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
    --shadow:
      2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
    --shadow-md:
      2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 2px 4px 3px hsl(0 0% 0% / 0.18);
    --shadow-lg:
      2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 4px 6px 3px hsl(0 0% 0% / 0.18);
    --shadow-xl:
      2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 8px 10px 3px hsl(0 0% 0% / 0.18);
    --shadow-2xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.45);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
  }

  .dashboard-theme body,
  .dashboard-theme {
    font-family: var(--font-sans);
  }

  /* Prevent HTML/Body scroll ONLY when dashboard theme is active */
  body:has(.dashboard-theme) {
    overflow: hidden;
  }
  html:has(body:has(.dashboard-theme)) {
    overflow: hidden;
  }
}

/* Custom react-tweet styles */
.react-tweet-theme {
  --tweet-container-margin: 0 !important;
  --tweet-container-padding: 0 !important;
  --tweet-body-padding: 0 !important;
  --tweet-container-border: none !important;
  --tweet-container-border-radius: 0 !important;
  --tweet-bg-color: #000 !important;
  --tweet-text-color: #fff !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Ensure the inner container also respects theme */
.react-tweet {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background-color: var(
    --tweet-bg-color,
    #000
  ) !important; /* Use variable for consistency */
}

/* Fix hover states for dark mode - override light mode colors */
.dark .hover\:bg-gray-50:hover,
.dashboard-theme .hover\:bg-gray-50:hover,
.dark .dashboard-theme .hover\:bg-gray-50:hover {
  background-color: hsl(var(--muted)) !important;
}

/* Fix status badge colors for dark mode */
.dark .bg-green-100,
.dashboard-theme .bg-green-100,
.dark .dashboard-theme .bg-green-100 {
  background-color: hsl(142 76% 15%) !important;
}

.dark .text-green-800,
.dashboard-theme .text-green-800,
.dark .dashboard-theme .text-green-800 {
  color: hsl(142 76% 75%) !important;
}

.dark .bg-yellow-100,
.dashboard-theme .bg-yellow-100,
.dark .dashboard-theme .bg-yellow-100 {
  background-color: hsl(48 96% 15%) !important;
}

.dark .text-yellow-800,
.dashboard-theme .text-yellow-800,
.dark .dashboard-theme .text-yellow-800 {
  color: hsl(48 96% 75%) !important;
}

.dark .bg-red-100,
.dashboard-theme .bg-red-100,
.dark .dashboard-theme .bg-red-100 {
  background-color: hsl(0 84% 15%) !important;
}

.dark .text-red-800,
.dashboard-theme .text-red-800,
.dark .dashboard-theme .text-red-800 {
  color: hsl(0 84% 75%) !important;
}

.dark .bg-gray-100,
.dashboard-theme .bg-gray-100,
.dark .dashboard-theme .bg-gray-100 {
  background-color: hsl(var(--muted)) !important;
}

.dark .text-gray-800,
.dashboard-theme .text-gray-800,
.dark .dashboard-theme .text-gray-800 {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .text-gray-600,
.dashboard-theme .text-gray-600,
.dark .dashboard-theme .text-gray-600 {
  color: hsl(var(--muted-foreground)) !important;
}

/* Remove hover state from table header rows */
thead tr:hover,
thead tr.hover\:bg-muted\/50:hover,
thead tr.hover\:bg-gray-50:hover {
  background-color: transparent !important;
}

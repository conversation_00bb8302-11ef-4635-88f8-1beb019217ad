"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Updated imports to point to src/components/dashboard
import { StrackrOverview } from "@/components/dashboard/strackr-overview"
import { ShopMyOverview } from "@/components/dashboard/shopmy-overview"
import { StrackrMetrics } from "@/components/dashboard/strackr-metrics"
import { ShopMyMetrics } from "@/components/dashboard/shopmy-metrics"
import { AggregateMetrics } from "@/components/dashboard/aggregate-metrics"
import { PlatformFilters } from "@/components/dashboard/platform-filters"

// Import the hook for analytics data
import { usePlatformAnalytics, TransactionFilters as TransactionFiltersType } from "@/hooks/usePlatformAnalytics";

// No date adjustments needed - use dates as-is

export default function HomePageClient() {
  // State for time ranges - each platform can have its own time range
  const [strackrTimeRange, setStrackrTimeRange] = React.useState("all");
  const [shopMyTimeRange, setShopMyTimeRange] = React.useState("all");

  // Individual filter state for each platform
  const [strackrFilters, setStrackrFilters] = React.useState<Omit<TransactionFiltersType, 'platform'>>({});
  const [shopMyFilters, setShopMyFilters] = React.useState<Omit<TransactionFiltersType, 'platform'>>({});

  // Fetch analytics data for both platforms with time ranges and transaction filters
  const {
    data: strackrData,
    loading: strackrLoading,
    error: strackrError,
    refetch: refetchStrackr
  } = usePlatformAnalytics('strackr', strackrTimeRange, undefined, undefined, strackrFilters);

  const {
    data: shopMyData,
    loading: shopMyLoading,
    error: shopMyError,
    refetch: refetchShopMy
  } = usePlatformAnalytics('shopmy', shopMyTimeRange, undefined, undefined, shopMyFilters);

  // Handle time range changes
  const handleStrackrTimeRangeChange = React.useCallback((timeRange: string) => {
    setStrackrTimeRange(timeRange);
  }, []);

  const handleShopMyTimeRangeChange = React.useCallback((timeRange: string) => {
    setShopMyTimeRange(timeRange);
  }, []);

  // Handle platform-specific filter changes
  const handleStrackrFiltersChange = React.useCallback((filters: Omit<TransactionFiltersType, 'platform'>) => {
    setStrackrFilters(filters);
  }, []);

  const handleShopMyFiltersChange = React.useCallback((filters: Omit<TransactionFiltersType, 'platform'>) => {
    setShopMyFilters(filters);
  }, []);

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first (or wherever they were originally) */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Overview</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Two-column grid for Strackr and ShopMy analytics with individual filters */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        {/* Column 1: Strackr Section */}
        <div className="space-y-4">
          <PlatformFilters
            platform="strackr"
            onFiltersChange={handleStrackrFiltersChange}
            initialFilters={strackrFilters}
          />
          <StrackrOverview
            analyticsData={strackrData}
            loading={strackrLoading}
            error={strackrError}
            timeRange={strackrTimeRange}
            onTimeRangeChange={handleStrackrTimeRangeChange}
          />
        </div>

        {/* Column 2: ShopMy Section */}
        <div className="space-y-4">
          <PlatformFilters
            platform="shopmy"
            onFiltersChange={handleShopMyFiltersChange}
            initialFilters={shopMyFilters}
          />
          <ShopMyOverview
            analyticsData={shopMyData}
            loading={shopMyLoading}
            error={shopMyError}
            timeRange={shopMyTimeRange}
            onTimeRangeChange={handleShopMyTimeRangeChange}
          />
        </div>
      </div>

      {/* Aggregate Metrics Section */}
      <AggregateMetrics
        shopMyData={shopMyData}
        strackrData={strackrData}
        loading={shopMyLoading || strackrLoading}
        error={shopMyError || strackrError}
      />

      {/* Shop My Quick Stats */}
      <ShopMyMetrics
        analyticsData={shopMyData}
        loading={shopMyLoading}
        error={shopMyError}
      />

      {/* Strackr Quick Stats */}
      <StrackrMetrics
        analyticsData={strackrData}
        loading={strackrLoading}
        error={strackrError}
      />
    </div>
  );
}

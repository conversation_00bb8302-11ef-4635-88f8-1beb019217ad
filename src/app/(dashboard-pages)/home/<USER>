import { createClient } from "@/supabase/client/server";
import { redirect } from "next/navigation";
import HomePageClient from "./home-client";

export default async function HomePage() {
  // Server-side authentication check
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    redirect('/sign-in');
  }

  // If authenticated, render the client component
  return <HomePageClient />;
}

import {
  B<PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { Linkbuilder } from "@/components/dashboard/linkbuilder";
import { createClient } from "@/supabase/client/server";

export default async function LinkbuilderPage() {
  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/home">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Linkbuilder</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Linkbuilder</h1>
        <p className="text-muted-foreground">
          Monitor affiliate link generation and manage networks for your links.
        </p>
      </div>

      <Linkbuilder />
    </div>
  );
}

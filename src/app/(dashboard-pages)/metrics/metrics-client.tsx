"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useMetrics } from "@/contexts/metrics-context";
import { OptimizedEmbed } from "@/components/embed-cache";

export default function MetricsPageClient() {
  const { activeTab, setActiveTab } = useMetrics();

  return (
    <div className="h-full w-full overflow-hidden flex flex-col">
      {/* Tab Toggle in Header */}
      <div className="flex-shrink-0 p-2 border-b">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="extension">Extension Metrics</TabsTrigger>
            <TabsTrigger value="mobile">Mobile Metrics</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content Area */}
      <div className="flex-1 h-full w-full overflow-hidden">
        {activeTab === "extension" && (
          <OptimizedEmbed embedId="metrics-extension" className="h-full w-full" />
        )}
        
        {activeTab === "mobile" && (
          <OptimizedEmbed embedId="metrics-mobile" className="h-full w-full" />
        )}
      </div>
    </div>
  );
}

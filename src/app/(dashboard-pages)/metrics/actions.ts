'use server';

import { createClient } from '@/supabase/client/server';

/* --------------------------------------------------------------------------
 * Types
 * --------------------------------------------------------------------------*/

// site_activation table ------------------------------------------------------
export interface SiteActivationRow {
  id: number;
  date: string; // YYYY-MM-DD
  site_domain: string | null;
  hostname: string | null;
  total_users: number | null;
  activated_users: number | null;
  activation_rate: number | null;
  phia_shown_count: number | null;
  phia_clicked_count: number | null;
  platform: string | null;
}

// onboarding_funnel table ----------------------------------------------------
export interface OnboardingFunnelRow {
  id: number;
  date: string; // YYYY-MM-DD
  funnel_step: string;
  step_order: number | null;
  users_count: number | null;
  conversion_rate: number | null;
  step1_users: number | null;
  step2_users: number | null;
  overall_conversion_rate: number | null;
}

// safari_extension_metrics table -------------------------------------------
export interface SafariExtensionMetricsRow {
  id: number;
  date: string; // YYYY-MM-DD
  metric_name: string;
  event_name: string;
  total_events: number | null;
  unique_users: number | null;
  platform: string | null;
}

// daily_metrics table -------------------------------------------------------
export interface DailyMetricsRow {
  id: number;
  date: string;
  event_name: string;
  total_events: number | null;
  unique_users: number | null;
  metric_type: string | null;
}

// cohort_retention table ----------------------------------------------------
export interface CohortRetentionRow {
  id: number;
  cohort_start_date: string;
  cohort_end_date: string | null;
  cohort_id: number | null;
  cohort_name: string | null;
  cohort_size: number | null;
  week_number: number | null;
  active_users: number | null;
  retention_rate: number | null;
  activation_event: string | null;
  retention_event: string | null;
  week_start_date: string | null;
  week_end_date: string | null;
}

/* --------------------------------------------------------------------------
 * Helper
 * --------------------------------------------------------------------------*/

async function getServerClient() {
  // Ensures we always create a new server-side Supabase client and the user session
  return await createClient();
}

/* --------------------------------------------------------------------------
 * Public API
 * --------------------------------------------------------------------------*/

// 1. Site Activation ---------------------------------------------------------
export async function fetchSiteActivation(
  {
    fromDate,
    toDate,
    platform = 'IOS_SAFARI_EXTENSION',
    limit = 20,
  }: {
    fromDate?: string; // inclusive YYYY-MM-DD
    toDate?: string;   // inclusive YYYY-MM-DD
    platform?: string;
    limit?: number;
  } = {},
): Promise<SiteActivationRow[]> {
  const supabase = await getServerClient();

  let query = supabase
    .from('site_activation')
    .select('*')
    .eq('platform', platform)
    .order('phia_shown_count', { ascending: false })
    .limit(limit);

  if (fromDate) query = query.gte('date', fromDate);
  if (toDate) query = query.lte('date', toDate);

  const { data, error } = await query;

  if (error) {
    console.error('[fetchSiteActivation] Supabase error:', error.message);
    return [];
  }

  return data as SiteActivationRow[];
}

// 2. Onboarding Funnel -------------------------------------------------------
export async function fetchOnboardingFunnel(
  { date }: { date?: string } = {},
): Promise<OnboardingFunnelRow[]> {
  const supabase = await getServerClient();

  let query = supabase
    .from('onboarding_funnel')
    .select('*')
    .order('step_order', { ascending: true });

  if (date) query = query.eq('date', date);

  const { data, error } = await query;
  if (error) {
    console.error('[fetchOnboardingFunnel] Supabase error:', error.message);
    return [];
  }
  return data as OnboardingFunnelRow[];
}

// 3. Safari Extension Metrics -----------------------------------------------
export async function fetchSafariExtensionMetrics(
  {
    metricName,
    fromDate,
    toDate,
  }: {
    metricName?: string;
    fromDate?: string;
    toDate?: string;
  } = {},
): Promise<SafariExtensionMetricsRow[]> {
  const supabase = await getServerClient();

  let query = supabase
    .from('safari_extension_metrics')
    .select('*')
    .eq('platform', 'IOS_SAFARI_EXTENSION')
    .order('date', { ascending: false });

  if (metricName) query = query.eq('metric_name', metricName);
  if (fromDate) query = query.gte('date', fromDate);
  if (toDate) query = query.lte('date', toDate);

  const { data, error } = await query;
  if (error) {
    console.error('[fetchSafariExtensionMetrics] Supabase error:', error.message);
    return [];
  }
  return data as SafariExtensionMetricsRow[];
}

// 4. Weekly Active Users (Daily Metrics) -------------------------------------
export async function fetchDailyMetrics(
  {
    metricType,
    fromDate,
    toDate,
  }: {
    metricType?: string; // e.g., 'heartbeat'
    fromDate?: string;
    toDate?: string;
  } = {},
): Promise<DailyMetricsRow[]> {
  const supabase = await getServerClient();

  let query = supabase.from('daily_metrics').select('*').order('date', { ascending: false });

  if (metricType) query = query.eq('metric_type', metricType);
  if (fromDate) query = query.gte('date', fromDate);
  if (toDate) query = query.lte('date', toDate);

  const { data, error } = await query;
  if (error) {
    console.error('[fetchDailyMetrics] Supabase error:', error.message);
    return [];
  }
  return data as DailyMetricsRow[];
}

// 5. Cohort Retention --------------------------------------------------------
export async function fetchCohortRetention(
  {
    cohortStartDate,
    activationEvent = 'phia_clicked',
  }: {
    cohortStartDate?: string;
    activationEvent?: string;
  } = {},
): Promise<CohortRetentionRow[]> {
  const supabase = await getServerClient();

  let query = supabase
    .from('cohort_retention')
    .select('*')
    .eq('activation_event', activationEvent)
    .order('week_number', { ascending: true });

  if (cohortStartDate) query = query.eq('cohort_start_date', cohortStartDate);

  const { data, error } = await query;
  if (error) {
    console.error('[fetchCohortRetention] Supabase error:', error.message);
    return [];
  }
  return data as CohortRetentionRow[];
}

/* --------------------------------------------------------------------------
 * Aggregated helper (example): fetchMetricsDashboard
 * --------------------------------------------------------------------------*/

export interface MetricsDashboardData {
  siteActivation: SiteActivationRow[];
  onboardingFunnel: OnboardingFunnelRow[];
  safariMetrics: SafariExtensionMetricsRow[];
  dailyMetrics: DailyMetricsRow[];
  cohortRetention: CohortRetentionRow[];
}

/**
 * Fetches all 5 datasets required for the dashboard in parallel.
 */
export async function fetchMetricsDashboard(): Promise<MetricsDashboardData> {
  const [siteActivation, onboardingFunnel, safariMetrics, dailyMetrics, cohortRetention] =
    await Promise.all([
      fetchSiteActivation(),
      fetchOnboardingFunnel(),
      fetchSafariExtensionMetrics(),
      fetchDailyMetrics({ metricType: 'heartbeat' }),
      fetchCohortRetention(),
    ]);

  return {
    siteActivation,
    onboardingFunnel,
    safariMetrics,
    dailyMetrics,
    cohortRetention,
  };
}

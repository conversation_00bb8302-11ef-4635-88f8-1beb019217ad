"use client";

import React from "react";
import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";


// Import upload components
import { CsvUploadDropzone } from "@/components/upload/csv-upload-dropzone";
import { UploadHistory } from "@/components/upload/upload-history";

export default function UploadPage() {
  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/home">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Upload Data</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Upload CSV Data</h1>
        <p className="text-muted-foreground">
          Upload your Shopmy CSV files to automatically import transaction data into your dashboard.
          Duplicate transactions will be automatically detected and skipped.
        </p>
      </div>

      {/* Main Content */}
      <div className="flex-1 space-y-8">
        {/* Upload Section */}
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Upload CSV File</CardTitle>
              <CardDescription>
                Drag and drop your Shopmy CSV file or click to browse.
                Maximum file size: 10MB.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CsvUploadDropzone />
            </CardContent>
          </Card>
        </div>

        {/* History Section */}
        <Card>
          <CardHeader>
            <CardTitle>Upload History</CardTitle>
            <CardDescription>
              View and manage your previous CSV uploads
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UploadHistory />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

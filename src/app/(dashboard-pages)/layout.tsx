import { cn } from "@/lib/utils"; // Assuming you have cn from shadcn
import React from "react";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"; // Import SidebarProvider and SidebarInset
import { AppSidebar } from "@/components/dashboard/app-sidebar"; // Import AppSidebar
import { SiteHeader } from "@/components/dashboard/site-header"; // Import SiteHeader
import { EmbedCacheProvider } from "@/components/embed-cache"; // Import EmbedCacheProvider
import { createClient } from "@/supabase/client/server";
import { redirect } from "next/navigation";

// This layout applies only to the /dashboard route and its children
// It ensures the dashboard doesn't inherit the main RootLayout's header/footer
// It only applies the theme class and the specific dashboard background.
export default async function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Server-side authentication check for all dashboard pages
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    redirect('/sign-in');
  }
  return (
    // Wrap everything in a div and apply the theme class here
    <div className={cn("dashboard-theme", "flex h-screen w-full")}>
      <EmbedCacheProvider>
        <SidebarProvider>
          <AppSidebar variant="inset" />
          <SidebarInset>
            {/* Removed dashboard-theme and bg-background from this inner div */}
            <div
              className={cn(
                // "dashboard-theme", // Removed theme class
                "flex h-full w-full flex-col" // Removed h-screen, bg-background, kept flex styles
              )}
            >
              {/* Make SiteHeader sticky */}
              <SiteHeader className="sticky top-0 z-10 bg-sidebar" />
              <div className="flex flex-1 flex-col overflow-y-auto"> {/* Added overflow-y-auto */}
                {children}
              </div>
            </div>
          </SidebarInset>
        </SidebarProvider>
      </EmbedCacheProvider>
    </div>
  );
}

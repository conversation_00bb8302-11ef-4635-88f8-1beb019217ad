import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { DailyPlatformSummary, MerchantPerformance, TransactionSummary, SummaryApiResponse } from '@/types/database';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const type = searchParams.get('type') || 'daily'; // daily, merchant, transaction
    const platform = searchParams.get('platform');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    
    let query;
    let tableName: string;
    
    // Determine which table to query based on type
    switch (type) {
      case 'daily':
        tableName = 'daily_platform_summary';
        query = supabaseAdmin
          .from(tableName)
          .select('*')
          .order('date', { ascending: false })
          .limit(limit);
        break;
        
      case 'merchant':
        tableName = 'merchant_performance';
        query = supabaseAdmin
          .from(tableName)
          .select('*')
          .order('total_commission', { ascending: false })
          .limit(limit);
        break;
        
      case 'transaction':
        tableName = 'transaction_summary';
        query = supabaseAdmin
          .from(tableName)
          .select('*')
          .order('transaction_day', { ascending: false })
          .limit(limit);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid summary type. Use: daily, merchant, or transaction' },
          { status: 400 }
        );
    }
    
    // Apply common filters
    if (platform) {
      query = query.eq('platform', platform);
    }
    
    // Apply date filters based on table structure
    if (startDate) {
      if (type === 'daily') {
        query = query.gte('date', startDate);
      } else if (type === 'transaction') {
        query = query.gte('transaction_day', startDate);
      } else if (type === 'merchant') {
        query = query.gte('last_transaction_date', startDate);
      }
    }
    
    if (endDate) {
      if (type === 'daily') {
        query = query.lte('date', endDate);
      } else if (type === 'transaction') {
        query = query.lte('transaction_day', endDate);
      } else if (type === 'merchant') {
        query = query.lte('last_transaction_date', endDate);
      }
    }
    
    // Execute the query
    const { data, error, count } = await query;
    
    if (error) {
      console.error('Supabase summary query error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch summary data', details: error.message },
        { status: 500 }
      );
    }
    
    const response: SummaryApiResponse = {
      data: data as DailyPlatformSummary[] | MerchantPerformance[] | TransactionSummary[],
      count: count || 0
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Summary API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET specific platform summary
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { platforms, dateRange } = body;
    
    if (!platforms || !Array.isArray(platforms)) {
      return NextResponse.json(
        { error: 'Platforms array is required' },
        { status: 400 }
      );
    }
    
    // Build query for multiple platforms
    let query = supabaseAdmin
      .from('daily_platform_summary')
      .select('*')
      .in('platform', platforms)
      .order('date', { ascending: false });
    
    if (dateRange?.start) {
      query = query.gte('date', dateRange.start);
    }
    
    if (dateRange?.end) {
      query = query.lte('date', dateRange.end);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Multi-platform query error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch platform data', details: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ data });
    
  } catch (error) {
    console.error('POST Summary API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

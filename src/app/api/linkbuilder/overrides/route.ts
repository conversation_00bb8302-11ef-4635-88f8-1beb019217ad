import { NextRequest, NextResponse } from "next/server";
import { MerchantOverride } from "@/types/linkbuilder";

export async function GET() {
  try {
    const phiaEndpoint = process.env.PHIA_INTERNAL_API_ENDPOINT;
    const phiaToken = process.env.X_PHIA_TOKEN;

    if (!phiaEndpoint || !phiaToken) {
      console.error(
        "Missing required environment variables: PHIA_INTERNAL_API_ENDPOINT or X_PHIA_TOKEN"
      );
      return NextResponse.json(
        { success: false, error: "Server configuration error" },
        { status: 500 }
      );
    }

    const response = await fetch(phiaEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-phia-token": phiaToken,
      },
      body: JSON.stringify({
        query: `query GetAllMerchantConfigs {
          getAllMerchantConfigs {
            domain
            isBlacklisted
            preferredNetworkId
            createdAt
            updatedAt
          }
        }`,
      }),
    });

    const result = await response.json();

    if (result.data && result.data.getAllMerchantConfigs) {
      return NextResponse.json({
        success: true,
        data: result.data.getAllMerchantConfigs,
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Failed to fetch merchant configs" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error fetching merchant overrides:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { domain, isBlacklisted, preferredNetworkId } = body;

    if (!domain) {
      return NextResponse.json(
        { success: false, error: "Domain is required" },
        { status: 400 }
      );
    }

    const phiaEndpoint = process.env.PHIA_INTERNAL_API_ENDPOINT;
    const phiaToken = process.env.X_PHIA_TOKEN;

    if (!phiaEndpoint || !phiaToken) {
      console.error(
        "Missing required environment variables: PHIA_INTERNAL_API_ENDPOINT or X_PHIA_TOKEN"
      );
      return NextResponse.json(
        { success: false, error: "Server configuration error" },
        { status: 500 }
      );
    }

    const input: any = { domain };
    if (typeof isBlacklisted === "boolean") {
      input.isBlacklisted = isBlacklisted;
    }
    if (preferredNetworkId) {
      input.preferredNetworkId = preferredNetworkId;
    }

    const response = await fetch(phiaEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-phia-token": phiaToken,
      },
      body: JSON.stringify({
        query: `mutation CreateOrUpdateMerchantConfig($input: CreateOrUpdateMerchantConfigInput!) {
          createOrUpdateMerchantConfig(input: $input) {
            config {
              domain
              isBlacklisted
              preferredNetworkId
              createdAt
              updatedAt
            }
            isNew
          }
        }`,
        variables: { input },
      }),
    });

    const result = await response.json();

    if (result.data && result.data.createOrUpdateMerchantConfig) {
      const { config, isNew } = result.data.createOrUpdateMerchantConfig;
      return NextResponse.json({
        success: true,
        message: isNew
          ? "Merchant override created successfully"
          : "Merchant override updated successfully",
        data: config,
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Failed to create/update merchant config" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating merchant override:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

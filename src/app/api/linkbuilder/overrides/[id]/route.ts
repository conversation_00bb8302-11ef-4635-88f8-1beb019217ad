import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const domain = id;

    if (!domain) {
      return NextResponse.json(
        { success: false, error: "Domain is required" },
        { status: 400 }
      );
    }

    const phiaEndpoint = process.env.PHIA_INTERNAL_API_ENDPOINT;
    const phiaToken = process.env.X_PHIA_TOKEN;

    if (!phiaEndpoint || !phiaToken) {
      console.error(
        "Missing required environment variables: PHIA_INTERNAL_API_ENDPOINT or X_PHIA_TOKEN"
      );
      return NextResponse.json(
        { success: false, error: "Server configuration error" },
        { status: 500 }
      );
    }

    const response = await fetch(phiaEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-phia-token": phiaToken,
      },
      body: JSON.stringify({
        query: `mutation DeleteMerchantConfig($domain: String!) {
          deleteMerchantConfig(domain: $domain) {
            success
            message
          }
        }`,
        variables: { domain },
      }),
    });

    const result = await response.json();

    if (result.data && result.data.deleteMerchantConfig) {
      const { success, message } = result.data.deleteMerchantConfig;
      if (success) {
        return NextResponse.json({
          success: true,
          message: message || "Merchant override deleted successfully",
        });
      } else {
        return NextResponse.json(
          {
            success: false,
            error: message || "Failed to delete merchant config",
          },
          { status: 400 }
        );
      }
    } else {
      return NextResponse.json(
        { success: false, error: "Failed to delete merchant config" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error deleting merchant override:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

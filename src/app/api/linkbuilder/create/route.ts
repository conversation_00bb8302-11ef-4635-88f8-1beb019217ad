import { NextRequest, NextResponse } from "next/server";
import { validateUrl } from "@/utils/utils";

export async function POST(request: NextRequest) {
  try {
    const { url, device = "MOBILE" } = await request.json();

    const validation = validateUrl(url);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    const phiaEndpoint = process.env.PHIA_INTERNAL_API_ENDPOINT;
    const phiaToken = process.env.X_PHIA_TOKEN;

    if (!phiaEndpoint || !phiaToken) {
      console.error(
        "Missing required environment variables: PHIA_INTERNAL_API_ENDPOINT or X_PHIA_TOKEN"
      );
      return NextResponse.json(
        { success: false, error: "Server configuration error" },
        { status: 500 }
      );
    }

    const response = await fetch(phiaEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-phia-token": phiaToken,
      },
      body: JSON.stringify({
        query: `mutation CreateInternalMerchantUrl($input: CreateInternalMerchantUrlInput!) {
          createInternalMerchantUrl(input: $input) {
            merchantUrl {
              id
              merchantId
              url
              platform
              phiaId
              searchId
              productName
              productColor
              directLink
              createdAt
            }
            availableMerchants {
              advertiserId
              networkId
              networkName
              device
              commissionRateMax
              networkRank
              evergreenUrl
              evergreenUrlOverride
              cookieDurationHours
              domain
              name
              website
            }
          }
        }`,
        variables: {
          input: {
            website: url.trim(),
            device: device,
          },
        },
      }),
    });

    const result = await response.json();

    if (result.data && result.data.createInternalMerchantUrl) {
      const { merchantUrl, availableMerchants } =
        result.data.createInternalMerchantUrl;

      return NextResponse.json({
        success: true,
        data: {
          merchantUrl,
          availableMerchants,
        },
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Failed to create merchant URL" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating merchant URL:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/supabase/client/server';
import { resetShopMyData } from '@/app/(dashboard-pages)/home/<USER>';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Add additional security check - only allow specific users or add confirmation
    const body = await request.json();
    const { confirmation } = body;

    if (confirmation !== 'RESET_SHOPMY_DATA') {
      return NextResponse.json(
        { error: 'Invalid confirmation. Please provide the correct confirmation string.' },
        { status: 400 }
      );
    }

    // Execute the reset
    const result = await resetShopMyData();

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} ShopMy records`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    console.error('Reset ShopMy API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
